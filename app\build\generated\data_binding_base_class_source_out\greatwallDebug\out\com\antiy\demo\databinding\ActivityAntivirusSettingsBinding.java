// Generated by view binder compiler. Do not edit!
package com.antiy.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.antiy.demo.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAntivirusSettingsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final Button btnClearCache;

  @NonNull
  public final MaterialButton btnManageBlackWhiteList;

  @NonNull
  public final Button btnSetCache;

  @NonNull
  public final ImageView ivArrow;

  @NonNull
  public final RelativeLayout layoutCacheSize;

  @NonNull
  public final RelativeLayout layoutCustomPath;

  @NonNull
  public final Spinner spinnerCacheValidity;

  @NonNull
  public final SwitchMaterial switchBlacklist;

  @NonNull
  public final SwitchMaterial switchWhitelist;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCacheSize;

  @NonNull
  public final TextView tvSelectedPath;

  private ActivityAntivirusSettingsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageButton btnBack, @NonNull Button btnClearCache,
      @NonNull MaterialButton btnManageBlackWhiteList, @NonNull Button btnSetCache,
      @NonNull ImageView ivArrow, @NonNull RelativeLayout layoutCacheSize,
      @NonNull RelativeLayout layoutCustomPath, @NonNull Spinner spinnerCacheValidity,
      @NonNull SwitchMaterial switchBlacklist, @NonNull SwitchMaterial switchWhitelist,
      @NonNull Toolbar toolbar, @NonNull TextView tvCacheSize, @NonNull TextView tvSelectedPath) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnClearCache = btnClearCache;
    this.btnManageBlackWhiteList = btnManageBlackWhiteList;
    this.btnSetCache = btnSetCache;
    this.ivArrow = ivArrow;
    this.layoutCacheSize = layoutCacheSize;
    this.layoutCustomPath = layoutCustomPath;
    this.spinnerCacheValidity = spinnerCacheValidity;
    this.switchBlacklist = switchBlacklist;
    this.switchWhitelist = switchWhitelist;
    this.toolbar = toolbar;
    this.tvCacheSize = tvCacheSize;
    this.tvSelectedPath = tvSelectedPath;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAntivirusSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAntivirusSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_antivirus_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAntivirusSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnBack;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btnClearCache;
      Button btnClearCache = ViewBindings.findChildViewById(rootView, id);
      if (btnClearCache == null) {
        break missingId;
      }

      id = R.id.btnManageBlackWhiteList;
      MaterialButton btnManageBlackWhiteList = ViewBindings.findChildViewById(rootView, id);
      if (btnManageBlackWhiteList == null) {
        break missingId;
      }

      id = R.id.btnSetCache;
      Button btnSetCache = ViewBindings.findChildViewById(rootView, id);
      if (btnSetCache == null) {
        break missingId;
      }

      id = R.id.ivArrow;
      ImageView ivArrow = ViewBindings.findChildViewById(rootView, id);
      if (ivArrow == null) {
        break missingId;
      }

      id = R.id.layoutCacheSize;
      RelativeLayout layoutCacheSize = ViewBindings.findChildViewById(rootView, id);
      if (layoutCacheSize == null) {
        break missingId;
      }

      id = R.id.layoutCustomPath;
      RelativeLayout layoutCustomPath = ViewBindings.findChildViewById(rootView, id);
      if (layoutCustomPath == null) {
        break missingId;
      }

      id = R.id.spinnerCacheValidity;
      Spinner spinnerCacheValidity = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCacheValidity == null) {
        break missingId;
      }

      id = R.id.switchBlacklist;
      SwitchMaterial switchBlacklist = ViewBindings.findChildViewById(rootView, id);
      if (switchBlacklist == null) {
        break missingId;
      }

      id = R.id.switchWhitelist;
      SwitchMaterial switchWhitelist = ViewBindings.findChildViewById(rootView, id);
      if (switchWhitelist == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvCacheSize;
      TextView tvCacheSize = ViewBindings.findChildViewById(rootView, id);
      if (tvCacheSize == null) {
        break missingId;
      }

      id = R.id.tvSelectedPath;
      TextView tvSelectedPath = ViewBindings.findChildViewById(rootView, id);
      if (tvSelectedPath == null) {
        break missingId;
      }

      return new ActivityAntivirusSettingsBinding((CoordinatorLayout) rootView, btnBack,
          btnClearCache, btnManageBlackWhiteList, btnSetCache, ivArrow, layoutCacheSize,
          layoutCustomPath, spinnerCacheValidity, switchBlacklist, switchWhitelist, toolbar,
          tvCacheSize, tvSelectedPath);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
