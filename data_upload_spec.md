### **长城私有云车机SDK数据回传功能技术文档**

#### 1. 背景

为了更好地了解和支持在长城私有云上运行我们SDK的设备情况，并为后续的产品优化、数据分析和运营决策提供数据支持，我们需要在现有的车机（In-Vehicle Infotainment, IVI）SDK中增加数据回传功能。本文档旨在阐述该功能的技术实现方案，包括回传数据的具体内容、获取方式以及回传时机。

#### 2. 数据回传规范

每次回传需要包含以下设备信息：

| 字段 | 英文标识 | 数据类型 | 说明 |
| :--- | :--- | :--- | :--- |
| 设备品牌 | `brand` | String | 设备的品牌，例如 "Haval", "WEY" 等。 |
| 设备型号 | `model` | String | 设备的具体型号。 |
| 设备语言 | `language` | String | 设备当前设置的系统语言。 |
| 设备唯一标识 | `deviceId` | String | 用于唯一识别一台设备的ID。 |

#### 3. 数据获取方案 (Android)

##### 3.1 设备品牌 (Brand)

可以通过读取 Android 系统的 `Build` 类来获取。

**代码示例:**
```java
import android.os.Build;

String deviceBrand = Build.BRAND;
```

##### 3.2 设备型号 (Model)

同样通过 `Build` 类获取。

**代码示例:**
```java
import android.os.Build;

String deviceModel = Build.MODEL;
```

##### 3.3 设备语言 (Language)

可以通过 `Locale` 类获取设备当前设置的语言。建议使用 `toLanguageTag()` 方法获取符合 IETF BCP 47 标准的语言标签（例如 "zh-CN"）。

**代码示例:**
```java
import java.util.Locale;

// 获取默认的区域设置
Locale currentLocale = Locale.getDefault(); 
// 获取语言的 BCP 47 标签
String languageTag = currentLocale.toLanguageTag(); 
```

##### 3.4 设备唯一标识 (Unique Device Identifier)

设备唯一标识是数据回传中的关键。在Android生态中，获取一个稳定、唯一且合规的设备标识需要综合考虑多种因素，如系统版本、用户隐私和权限等。

**方案对比:**

1.  **`ANDROID_ID`**:
    *   **优点**: 获取简单，无需特殊权限。
    *   **缺点**:
        *   在 Android 8.0 (API 26) 以下的设备，恢复出厂设置会重置 `ANDROID_ID`。
        *   在 Android 8.0 及以上版本，`ANDROID_ID` 的值是根据应用签名密钥、用户和设备来确定的。不同应用获取到的值不同。
        *   在某些设备上可能返回 `null`。
    *   **结论**: 不够稳定和唯一，不推荐作为主要标识。

2.  **IMEI/IMSI**:
    *   **优点**: 对具有电话功能的设备来说是唯一的。
    *   **缺点**:
        *   需要 `READ_PHONE_STATE` 危险权限，会引起用户警惕。
        *   Android 10 (API 29) 开始，普通应用无法获取 IMEI。
        *   不适用于没有电话功能的设备（如仅有Wi-Fi的平板或车机）。
    *   **结论**: 权限要求高，合规风险大，且不通用，**强烈不推荐**。

3.  **首次启动时生成的UUID (推荐方案)**:
    *   **思路**: 在SDK首次初始化时，生成一个标准的 `UUID` (Universally Unique Identifier)，并将其持久化存储在设备本地（如 `SharedPreferences` 或内部存储文件）。后续每次启动都读取这个存储的UUID作为设备标识。
    *   **优点**:
        *   **唯一性**: UUID的碰撞概率极低，可以保证全局唯一。
        *   **稳定性**: 只要应用不被卸载或数据不被清除，该ID就保持不变。
        *   **合规性**: 无需任何敏感权限，不涉及用户隐私信息。
        *   **通用性**: 适用于所有Android设备。
    *   **缺点**: 应用卸载重装后，ID会重新生成。但在车机场景下，应用通常是预装的，卸载频率极低，因此这个缺点可以接受。

**推荐方案实现:**
```java
import android.content.Context;
import android.content.SharedPreferences;
import java.util.UUID;

public class DeviceIdManager {

    private static final String PREF_UNIQUE_ID = "PREF_UNIQUE_ID";
    private static String uniqueID = null;
    private static final String FILENAME = "DEVICE_ID.txt";

    public synchronized static String getDeviceId(Context context) {
        if (uniqueID == null) {
            SharedPreferences sharedPrefs = context.getSharedPreferences(
                    FILENAME, Context.MODE_PRIVATE);
            uniqueID = sharedPrefs.getString(PREF_UNIQUE_ID, null);

            if (uniqueID == null) {
                uniqueID = UUID.randomUUID().toString();
                SharedPreferences.Editor editor = sharedPrefs.edit();
                editor.putString(PREF_UNIQUE_ID, uniqueID);
                editor.apply();
            }
        }
        return uniqueID;
    }
}
```
**使用方式:**
在SDK的初始化代码中调用 `DeviceIdManager.getDeviceId(context)` 即可获取设备唯一标识。

#### 4. 数据回传时机

采用 **SDK初始化时启动子线程回传** 的方案，确保数据上报不影响主流程。

*   **实现逻辑**:
    1.  SDK在每次初始化时，都会启动一个独立的子线程。
    2.  子线程负责执行完整的数据回传逻辑：
        a. 检查本地是否已保存过设备唯一标识 (`deviceId`)。
        b. 如果 `deviceId` 不存在，则生成ID并保存。
        c. 收集完整的设备信息（品牌、型号、语言、deviceId）。
        d. 将设备信息异步回传到后端服务器。
    3.  将回传操作放在子线程中，可以避免阻塞SDK的主初始化流程，确保即使在网络不佳或服务器响应慢的情况下，也不会影响应用的启动性能。

*   **优点**:
    *   **不阻塞主线程**: 异步回传，不影响SDK初始化速度和应用性能。
    *   **逻辑解耦**: 回传逻辑与初始化主流程分离，代码更清晰。
    *   **信息更新**: 可以配置策略，定期更新设备信息（如语言变更），而不仅仅是首次。

*   **缺点**:
    *   **实现稍复杂**: 需要处理线程管理。
    *   **潜在的重复上报**: 如果不加控制，每次初始化都上报会增加服务器压力。但可以通过增加“每日一次”等策略来优化。

*   **结论**:
    该方案在确保不影响核心功能性能的前提下，完成了数据回传，且为后续更灵活的上报策略（如定期更新）提供了基础。

#### 5. 回传时序图

```mermaid
sequenceDiagram
    participant App as "App (Main Thread)"
    participant SDK as "车机SDK"
    participant BGThread as "SDK Background Thread"
    participant LocalStorage as "本地存储"
    participant Server as "后端服务器"

    App->>SDK: 调用初始化
    SDK->>BGThread: 启动子线程进行数据回传
    SDK-->>App: 初始化方法立即返回
    note over App: 初始化完成，不被阻塞

    par 数据回传
        BGThread->>LocalStorage: 读取 deviceId
        LocalStorage-->>BGThread: 返回 deviceId (可能为null)
        alt deviceId 为 null
            BGThread->>BGThread: 生成新的 UUID 作为 deviceId
            BGThread->>LocalStorage: 保存新的 deviceId
        end
        BGThread->>BGThread: 收集设备信息 (品牌, 型号等)
        BGThread->>+Server: 异步回传设备信息
        Server-->>-BGThread: 响应成功
    end
```

#### 6. 回传流程图

```mermaid
graph TD
    subgraph Main Thread
        A[开始: SDK 初始化] --> B[启动数据回传子线程];
        B --> C[结束: 初始化完成];
    end

    subgraph Background Thread
        D[开始回传] --> E{读取本地 deviceId};
        E --> F{deviceId 是否存在?};
        F -- 否 --> G[生成 UUID 并保存];
        G --> H[收集设备信息];
        F -- 是 --> H;
        H --> I[回传数据到服务器];
        I --> J[结束回传];
    end
```

#### 7. 工期评估 (表格)

| 阶段 | 主要内容 | 预计工时 (人/天) | 备注 |
| :--- | :--- | :--- | :--- |
| **方案设计** | 技术文档撰写与评审 | 1 | 已完成 |
| **功能开发** | SDK端数据采集与上报逻辑实现 | 1 | 核心编码工作 |
| **联调测试** | 与后端进行接口联调 | 1 | 依赖后端接口 |

| **总计** | | **4** | |



#### 8. 工期排期 (甘特图)

```mermaid
gantt
    title 数据回传功能开发排期
    dateFormat  YYYY-MM-DD
    
    section 规划阶段
    方案设计与评审 :done, des1, 2025-06-19, 1d

    section 执行阶段
    功能开发 :dev1, after des1, 0.5d
    联调测试 :test1, after dev1, 0.5d
```

