1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.antiy.demo"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
8-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:5:5-80
11-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:5:22-77
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:6:5-81
12-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:7:5-68
13-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:8:5-79
14-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
15-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:9:5-82
15-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:9:22-79
16
17    <application
17-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:11:5-85:19
18        android:name="com.antiy.demo.App"
18-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:12:9-28
19        android:allowBackup="true"
19-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:13:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1603cc54447b33400529d3919c6310\transformed\core-1.7.0\AndroidManifest.xml:24:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:14:9-65
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:fullBackupContent="@xml/backup_rules"
24-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:15:9-54
25        android:icon="@mipmap/ic_launcher"
25-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:16:9-43
26        android:label="@string/app_name"
26-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:17:9-41
27        android:roundIcon="@mipmap/ic_launcher_round"
27-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:18:9-54
28        android:supportsRtl="true"
28-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:19:9-35
29        android:theme="@style/Theme.AntiVirus"
29-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:20:9-47
30        android:usesCleartextTraffic="true" >
30-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:21:9-44
31        <activity
31-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:24:9-44:20
32            android:name="com.antiy.demo.activity.HomeActivity"
32-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:25:13-50
33            android:exported="true" >
33-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:26:13-36
34            <intent-filter>
34-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:28:13-32:29
35                <action android:name="android.intent.action.MAIN" />
35-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:29:17-69
35-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:29:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:31:17-77
37-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:31:27-74
38            </intent-filter>
39            <intent-filter>
39-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:34:13-38:29
40                <action android:name="android.intent.action.VIEW" />
40-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:35:17-69
40-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:35:25-66
41
42                <category android:name="android.intent.category.DEFAULT" />
42-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:36:17-76
42-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:36:27-73
43
44                <data android:scheme="content" />
44-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:37:17-50
44-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:37:23-47
45            </intent-filter>
46            <intent-filter>
46-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:39:13-43:29
47                <action android:name="android.intent.action.SEND" />
47-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:40:17-69
47-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:40:25-66
48
49                <category android:name="android.intent.category.DEFAULT" />
49-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:36:17-76
49-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:36:27-73
50
51                <data android:mimeType="*/*" />
51-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:37:17-50
51-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:42:23-45
52            </intent-filter>
53        </activity>
54        <activity android:name="com.antiy.demo.activity.UuidActivity" />
54-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:45:9-47:44
54-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:46:13-50
55        <activity
55-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:48:9-51:44
56            android:name="com.antiy.demo.activity.ScanningActivity"
56-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:49:13-54
57            android:exported="false" />
57-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:50:13-37
58        <activity
58-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:52:9-55:44
59            android:name="com.antiy.demo.activity.AntivirusSettingsActivity"
59-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:53:13-63
60            android:exported="false" />
60-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:54:13-37
61        <activity
61-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:56:9-59:44
62            android:name="com.antiy.demo.activity.CloudScanSettingsActivity"
62-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:57:13-63
63            android:exported="false" />
63-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:58:13-37
64        <activity
64-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:60:9-63:44
65            android:name="com.antiy.demo.activity.PerformanceSettingsActivity"
65-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:61:13-65
66            android:exported="false" />
66-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:62:13-37
67        <activity
67-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:64:9-67:44
68            android:name="com.antiy.demo.activity.HelpFeedbackActivity"
68-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:65:13-58
69            android:exported="false" />
69-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:66:13-37
70        <activity
70-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:68:9-71:44
71            android:name="com.antiy.demo.activity.MainActivity"
71-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:69:13-50
72            android:exported="false" />
72-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:70:13-37
73        <activity
73-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:72:9-75:44
74            android:name="com.antiy.demo.activity.ScanResultActivity"
74-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:73:13-56
75            android:exported="false" />
75-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:74:13-37
76        <activity
76-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:76:9-80:44
77            android:name="com.antiy.demo.activity.AboutActivity"
77-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:77:13-51
78            android:exported="false"
78-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:78:13-37
79            android:theme="@style/Theme.About" />
79-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:79:13-47
80        <activity
80-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:81:9-84:44
81            android:name="com.antiy.demo.activity.BlackWhiteListManagementActivity"
81-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:82:13-70
82            android:exported="false" />
82-->D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:83:13-37
83
84        <provider
84-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
85            android:name="androidx.startup.InitializationProvider"
85-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:27:13-67
86            android:authorities="com.antiy.demo.androidx-startup"
86-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:28:13-68
87            android:exported="false" >
87-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:29:13-37
88            <meta-data
88-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
89                android:name="androidx.emoji2.text.EmojiCompatInitializer"
89-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:32:17-75
90                android:value="androidx.startup" />
90-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:33:17-49
91            <meta-data
91-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
92                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
92-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
93                android:value="androidx.startup" />
93-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
94        </provider>
95    </application>
96
97</manifest>
