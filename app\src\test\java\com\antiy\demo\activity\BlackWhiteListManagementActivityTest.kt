package com.antiy.demo.activity

import android.content.Intent
import androidx.test.core.app.ActivityScenario
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.antiy.demo.activity.BlackWhiteListManagementActivity.Companion.EXTRA_LIST_TYPE
import com.antiy.demo.activity.BlackWhiteListManagementActivity.Companion.TYPE_ALL
import com.antiy.demo.activity.BlackWhiteListManagementActivity.Companion.TYPE_BLACK_LIST
import com.antiy.demo.activity.BlackWhiteListManagementActivity.Companion.TYPE_WHITE_LIST
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * 黑白名单管理Activity的测试用例
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [28])
class BlackWhiteListManagementActivityTest {

    @Test
    fun testActivityLaunchWithWhiteListType() {
        val intent = Intent(ApplicationProvider.getApplicationContext(), BlackWhiteListManagementActivity::class.java)
        intent.putExtra(EXTRA_LIST_TYPE, TYPE_WHITE_LIST)
        
        ActivityScenario.launch<BlackWhiteListManagementActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                // 验证Activity正常启动
                assert(activity != null)
                // 验证标题设置正确
                assert(activity.supportActionBar?.title == "白名单管理")
            }
        }
    }

    @Test
    fun testActivityLaunchWithBlackListType() {
        val intent = Intent(ApplicationProvider.getApplicationContext(), BlackWhiteListManagementActivity::class.java)
        intent.putExtra(EXTRA_LIST_TYPE, TYPE_BLACK_LIST)
        
        ActivityScenario.launch<BlackWhiteListManagementActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                // 验证Activity正常启动
                assert(activity != null)
                // 验证标题设置正确
                assert(activity.supportActionBar?.title == "黑名单管理")
            }
        }
    }

    @Test
    fun testActivityLaunchWithAllType() {
        val intent = Intent(ApplicationProvider.getApplicationContext(), BlackWhiteListManagementActivity::class.java)
        intent.putExtra(EXTRA_LIST_TYPE, TYPE_ALL)
        
        ActivityScenario.launch<BlackWhiteListManagementActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                // 验证Activity正常启动
                assert(activity != null)
                // 验证标题设置正确
                assert(activity.supportActionBar?.title == "黑白名单管理")
            }
        }
    }

    @Test
    fun testStaticStartMethod() {
        // 测试静态启动方法不会抛出异常
        try {
            BlackWhiteListManagementActivity.start(
                ApplicationProvider.getApplicationContext(),
                TYPE_WHITE_LIST
            )
        } catch (e: Exception) {
            // 在测试环境中可能会抛出异常，但不应该是空指针异常
            assert(e !is NullPointerException)
        }
    }
}
