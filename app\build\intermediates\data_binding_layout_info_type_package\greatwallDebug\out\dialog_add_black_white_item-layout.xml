<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_black_white_item" modulePackage="com.antiy.demo" filePath="app\src\main\res\layout\dialog_add_black_white_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_black_white_item_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="74" endOffset="14"/></Target><Target id="@+id/etHash" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="34"/></Target><Target id="@+id/tvTypeLabel" view="TextView"><Expressions/><location startLine="27" startOffset="4" endLine="35" endOffset="34"/></Target><Target id="@+id/radioGroupType" view="RadioGroup"><Expressions/><location startLine="38" startOffset="4" endLine="61" endOffset="16"/></Target><Target id="@+id/radioWhite" view="com.google.android.material.radiobutton.MaterialRadioButton"><Expressions/><location startLine="45" startOffset="8" endLine="51" endOffset="64"/></Target><Target id="@+id/radioBlack" view="com.google.android.material.radiobutton.MaterialRadioButton"><Expressions/><location startLine="53" startOffset="8" endLine="59" endOffset="62"/></Target></Targets></Layout>