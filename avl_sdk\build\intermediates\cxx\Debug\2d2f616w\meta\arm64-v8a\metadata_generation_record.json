[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: arm64-v8a", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\src\\main\\cpp\\CMakeLists.txt", "tag_": "baseDebug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 74195239}, {"level_": 0, "message_": "JSON 'D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\.cxx\\Debug\\2d2f616w\\arm64-v8a\\android_gradle_build.json' was up-to-date", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\src\\main\\cpp\\CMakeLists.txt", "tag_": "baseDebug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2029317356}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\src\\main\\cpp\\CMakeLists.txt", "tag_": "baseDebug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1052815313}]