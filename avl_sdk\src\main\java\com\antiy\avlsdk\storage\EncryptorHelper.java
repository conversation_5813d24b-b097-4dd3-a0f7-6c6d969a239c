package com.antiy.avlsdk.storage;

import android.security.keystore.KeyGenParameterSpec;
import android.security.keystore.KeyProperties;
import android.util.Base64;

import com.antiy.avlsdk.AVLCoreEngine;
import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.scan.FileChecker;
import com.antiy.avlsdk.utils.AppUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: EncryptorHelper
 * 4 * Author: wangbiao
 * 5 * Date: 2024/9/3 16:27
 * 6 * Description:负责数据加密和解密的工具类
 * 10
 */
public class EncryptorHelper {

    private static final String KEY_ALIAS = "avl_secret_key_alias";
    private static final String ANDROID_KEYSTORE = "AndroidKeyStore";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12; // GCM标准建议IV长度为12字节
    private static final int GCM_TAG_LENGTH = 128; // GCM标准建议Tag长度为128位

    public EncryptorHelper() {
        generateKey(); // 确保密钥已生成
    }

    /**
     * 把uuid进行md5加密
     * @param input 加密字符串
     * @return
     */
    public static String md5(String input) {
        try {
            // 获取MD5算法实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算输入字符串的MD5哈希值
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转化为十六进制格式的字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                // 转换成正数，避免负值干扰
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    private void generateKey() {
        try {
            KeyStore keyStore = KeyStore.getInstance(ANDROID_KEYSTORE);
            keyStore.load(null);

            if (!keyStore.containsAlias(KEY_ALIAS)) {
                KeyGenerator keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEYSTORE);
                keyGenerator.init(new KeyGenParameterSpec.Builder(
                        KEY_ALIAS,
                        KeyProperties.PURPOSE_ENCRYPT | KeyProperties.PURPOSE_DECRYPT)
                        .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                        .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                        .build());
                keyGenerator.generateKey();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String encrypt(String data) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            SecretKey key = getSecretKey();
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] iv = cipher.getIV(); // 初始化向量
            byte[] encryption = cipher.doFinal(data.getBytes());

            byte[] combined = new byte[iv.length + encryption.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encryption, 0, combined, iv.length, encryption.length);

            return Base64.encodeToString(combined, Base64.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String decrypt(String encryptedData) {
        try {
            byte[] combined = Base64.decode(encryptedData, Base64.DEFAULT);
            byte[] iv = new byte[GCM_IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, iv.length);
            byte[] encryption = new byte[combined.length - GCM_IV_LENGTH];
            System.arraycopy(combined, GCM_IV_LENGTH, encryption, 0, encryption.length);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            SecretKey key = getSecretKey();
            GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.DECRYPT_MODE, key, spec);
            byte[] decrypted = cipher.doFinal(encryption);

            return new String(decrypted);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private SecretKey getSecretKey() throws Exception {
        KeyStore keyStore = KeyStore.getInstance(ANDROID_KEYSTORE);
        keyStore.load(null);
        return ((KeyStore.SecretKeyEntry) keyStore.getEntry(KEY_ALIAS, null)).getSecretKey();
    }


    /**
     * 计算文件的SHA-256哈希值
     * @param filePath 文件路径
     * @return 文件的SHA-256哈希值
     * @throws IOException 读取文件异常
     * @throws NoSuchAlgorithmException 算法异常
     */
    public static String calcPathSHA256(String filePath){
        if (FileChecker.getInstance().isApkFile(new File(filePath))){
            //拿包名+hashCode(签名key) 真题做sha256 filePath="/sdcard/Test/4D489996114B287C01601421E8E355DD",  "com.android.cicibox" + "box5e75e0d1"
            String packName = AppUtil.getPackageNameFromApk(filePath);
            AVLEngine.Logger.info("calcPathSHA256 packageName:" + packName);
            String encryptData = packName + AVLCoreEngine.getInstance().getCertFullHash(filePath);
            return sha256(encryptData);//599318d587ab32910c34a139faba1b851eded16a2dfd17960da454320875c369
        }else{
            return sha256(new File(filePath));
        }
    }

    private static String sha256(String data){
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();

            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if(hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            AVLEngine.Logger.error("SHA-256 algorithm not found");
        }
        return "";
    }

    private static String sha256(File file) {
        if (!file.exists() || !file.isFile()) {
            return "";
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = fis.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }

            // Convert the byte array to a hexadecimal string
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest.digest()) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();

        } catch (IOException | NoSuchAlgorithmException e) {
            e.printStackTrace();
            AVLEngine.Logger.error("SHA-256 algorithm not found");
            return "";
        }
    }
}

