<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_antivirus_settings" modulePackage="com.antiy.demo" filePath="app\src\main\res\layout\activity_antivirus_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_antivirus_settings_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="298" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="39" endOffset="43"/></Target><Target id="@+id/btnBack" view="ImageButton"><Expressions/><location startLine="23" startOffset="12" endLine="29" endOffset="49"/></Target><Target id="@+id/layoutCustomPath" view="RelativeLayout"><Expressions/><location startLine="75" startOffset="20" endLine="107" endOffset="36"/></Target><Target id="@+id/tvSelectedPath" view="TextView"><Expressions/><location startLine="92" startOffset="24" endLine="99" endOffset="53"/></Target><Target id="@+id/ivArrow" view="ImageView"><Expressions/><location startLine="101" startOffset="24" endLine="106" endOffset="68"/></Target><Target id="@+id/switchWhitelist" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="153" startOffset="24" endLine="157" endOffset="66"/></Target><Target id="@+id/switchBlacklist" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="180" startOffset="24" endLine="184" endOffset="66"/></Target><Target id="@+id/btnManageBlackWhiteList" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="187" startOffset="20" endLine="197" endOffset="47"/></Target><Target id="@+id/spinnerCacheValidity" view="Spinner"><Expressions/><location startLine="245" startOffset="24" endLine="249" endOffset="66"/></Target><Target id="@+id/layoutCacheSize" view="RelativeLayout"><Expressions/><location startLine="252" startOffset="20" endLine="293" endOffset="36"/></Target><Target id="@+id/tvCacheSize" view="TextView"><Expressions/><location startLine="268" startOffset="24" endLine="275" endOffset="53"/></Target><Target id="@+id/btnSetCache" view="Button"><Expressions/><location startLine="277" startOffset="24" endLine="283" endOffset="47"/></Target><Target id="@+id/btnClearCache" view="Button"><Expressions/><location startLine="285" startOffset="24" endLine="292" endOffset="47"/></Target></Targets></Layout>