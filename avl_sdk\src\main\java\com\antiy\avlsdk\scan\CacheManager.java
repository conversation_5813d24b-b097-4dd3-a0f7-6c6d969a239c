package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.CacheEntity;
import com.antiy.avlsdk.storage.DataManager;

import java.io.File;

/**
 * Author: wangbiao
 * Date: 2024/9/9 10:08
 * Description:
 */
public class CacheManager {
    /**
     * 判断是否有缓存
     * @param path 文件路径
     * @return true有，false否
     */
    public static boolean hasCache(String path){
        return getCacheResult(path) != null;
    }

    /**
     * 从缓存取扫描对象
     * @param path 文件路径
     * @return 缓存对象
     */
    public static CacheEntity getCacheResult(String path){
        String key = getCacheKey(path);
        CacheEntity cache = DataManager.getInstance().getCacheData(key);
        AVLEngine.Logger.error("cache:" + cache);
        return cache;
    }

    /**
     * 保存文件扫描结果
     * @param path 文件路径
     * @param virName 病毒名称
     */
    public static void storeScanResult(String path,String virName,String sha256) {
        DataManager.getInstance().saveCacheData(getCacheKey(sha256), virName, System.currentTimeMillis());
    }

    /**
     * 根据文件的路径_文件大小_文件修改时间组成具有唯一性的key
     * @param path 文件路径
     * @return key的字符串
     */
    private static String getCacheKey(String path){
        return path;
    }
}
