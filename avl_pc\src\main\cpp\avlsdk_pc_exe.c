//
// Created by zos on 2024/9/25.
//

#include <dlfcn.h>
#include <err.h>
#include <errno.h>
#include <linux/limits.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <unistd.h>

#include "avlsdk_pc.h"

int main(int argc, char* argv[])
{
        char cwd[PATH_MAX];
        if (getcwd(cwd, sizeof(cwd)) == NULL) {
                fprintf(stderr, "getcwd() error: %s\n", strerror(errno));
                return -1;
        }

        char *avlsdk_so, *aid2name_so;
        asprintf(&avlsdk_so, "%s/%s", cwd, DEFAULT_SDK_PATH);
        asprintf(&aid2name_so, "%s/%s", cwd, DEFAULT_ID2NAME_PATH);

        if (access(avlsdk_so, F_OK) != 0 || access(aid2name_so, F_OK) != 0) {
                fprintf(stderr, "SDK library not found in provided directory %s.\n", cwd);
                return -1;
        }

        void* p_engine_handle = dlopen(avlsdk_so, RTLD_LAZY);
        void* p_i2n_handle    = dlopen(aid2name_so, RTLD_LAZY);
        if (!p_engine_handle || !p_i2n_handle) {
                char* errstr = dlerror();
                fprintf(stderr, "dlopen failed: %s\n", errstr);
                return -1;
        }

        P_AVL_SDK_Create             p_create         = NULL;
        P_AVL_SDK_Release            p_release        = NULL;
        P_AVL_SDK_Init               p_init           = NULL;
        P_AVL_SDK_LoadConfigFile     p_config         = NULL;
        P_AVL_SDK_SetConfigInt       p_set_cfg_int    = NULL;
        P_AVL_SDK_SetConfigString    p_set_cfg_str    = NULL;
        P_AVL_SDK_Scan               p_scan           = NULL;
        P_AVL_SDK_QueryReportInt     p_query_rpt_int  = NULL;
        P_AVL_SDK_QueryReportStr     p_query_rpt_str  = NULL;
        P_AVL_SDK_ReloadDB           p_reload         = NULL;
        P_AVL_SDK_QueryDBInfo        p_query_db_info  = NULL;
        P_AVL_NTranser_Init          p_i2n_init       = NULL;
        P_AVL_NTranser_Release       p_i2n_release    = NULL;
        P_AVL_NTranser_QueryNameByID p_i2n_query_name = NULL;

        // Get APIs
        p_create        = dlsym(p_engine_handle, "AVL_SDK_CreateInstance");
        p_release       = dlsym(p_engine_handle, "AVL_SDK_Release");
        p_init          = dlsym(p_engine_handle, "AVL_SDK_InitInstance");
        p_config        = dlsym(p_engine_handle, "AVL_SDK_LoadConfigFile");
        p_set_cfg_int   = dlsym(p_engine_handle, "AVL_SDK_SetConfigInt");
        p_set_cfg_str   = dlsym(p_engine_handle, "AVL_SDK_SetConfigString");
        p_scan          = dlsym(p_engine_handle, "AVL_SDK_Scan");
        p_query_rpt_int = dlsym(p_engine_handle, "AVL_SDK_QueryReportInt");
        p_query_rpt_str = dlsym(p_engine_handle, "AVL_SDK_QueryReportStr");
        p_reload        = dlsym(p_engine_handle, "AVL_SDK_ReloadDB");
        p_query_db_info = dlsym(p_engine_handle, "AVL_SDK_QueryDBInfo");


        if (p_create == NULL || p_release == NULL || p_init == NULL || p_config == NULL || p_set_cfg_int == NULL || p_set_cfg_str == NULL || p_scan == NULL ||
            p_query_rpt_int == NULL || p_query_rpt_str == NULL || p_reload == NULL) {
                fprintf(stderr, "Failed to load library functions...\n");
                return -1;
        }

        p_i2n_init       = dlsym(p_i2n_handle, "AVL_NTranser_Init");
        p_i2n_release    = dlsym(p_i2n_handle, "AVL_NTranser_Release");
        p_i2n_query_name = dlsym(p_i2n_handle, "AVL_NTranser_QueryNameByID");

        if (p_i2n_init == NULL || p_i2n_release == NULL || p_i2n_query_name == NULL) {
                fprintf(stderr, "Failed to load i2n functions...\n");
                return -1;
        }

        long long_last_ret = 0;

        // Create a new instance of AVLSDK
        void* p_engine_instance;
        long_last_ret = p_create(&p_engine_instance);
        if (long_last_ret != ERR_SUCCESS) {
                fprintf(stderr, "Engine creation failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }

        // Load the configuration template
        char* config_path;
        asprintf(&config_path, "%s/Config/high_scan.ct", cwd);
        long_last_ret = p_config(p_engine_instance, config_path);
        if (long_last_ret != ERR_SUCCESS) {
                fprintf(stderr, "LoadConfig failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }
        free(config_path);

        // Set the configuration by users
        long_last_ret = p_set_cfg_int(p_engine_instance, CFG_INT_APACK_RECURE_LAYER, 5);
        if (long_last_ret != ERR_SUCCESS) {
                fprintf(stderr, "SetConfig failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }

        // Set the configuration by users
        char* license_path;
        asprintf(&license_path, "%s/License.alf", cwd);
        long_last_ret = p_set_cfg_str(p_engine_instance, CFG_STR_LICENSE_PATH, license_path);
        if (long_last_ret != ERR_SUCCESS) {
                fprintf(stderr, "SetConfig failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }
        free(license_path);

        // Initialize the instance
        long_last_ret = p_init(p_engine_instance, NULL);
        if (long_last_ret != ERR_SUCCESS) {
                fprintf(stderr, "Init failed : %d\n", (int)long_last_ret);
                p_engine_instance = NULL;
                goto MAIN_OUT;
        }

        // Initialize the NameTranser instance
        void* p_i2n_instance;
        char* i2n_path;
        asprintf(&i2n_path, "%s/%s", cwd, DEFAULT_ID2N_DB_PATH);
        long_last_ret = p_i2n_init(i2n_path, &p_i2n_instance);
        if (long_last_ret != 0) {
                fprintf(stderr, "Init NameTranser failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }
        free(i2n_path);

        P_DB_INFO info;
        info = calloc(1, sizeof(*info));

        p_query_db_info(p_engine_instance, info);

        if (strlen(info->db_time_stamp) != 0) { printf("%s\n", info->db_time_stamp); }
        else {
                fprintf(stderr, "Failed to get db version!\n");
                goto MAIN_OUT;
        }

        dlclose(p_engine_handle);
        dlclose(p_i2n_handle);
        return 0;

MAIN_OUT:
        dlclose(p_engine_handle);
        dlclose(p_i2n_handle);

        return -1;
}
