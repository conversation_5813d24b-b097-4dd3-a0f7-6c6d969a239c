<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="update_frequency_options">
        <item>每天</item>
        <item>每周</item>
        <item>每月</item>
    </string-array>
    <color name="background_gray">#F5F5F5</color>
    <color name="background_light">#FAFAFA</color>
    <color name="black">#000000</color>
    <color name="danger_red">#F44336</color>
    <color name="divider">#E0E0E0</color>
    <color name="gray">#757575</color>
    <color name="light_blue">#E3F2FD</color>
    <color name="light_gray">#F5F5F5</color>
    <color name="primary">#4285F4</color>
    <color name="primary_variant">#1A73E8</color>
    <color name="processing_blue">#4285F4</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="safe_green">#4CAF50</color>
    <color name="secondary">#E3F2FD</color>
    <color name="success_green">#4CAF50</color>
    <color name="warning_yellow">#FFC107</color>
    <color name="white">#FFFFFF</color>
    <string name="about">关于</string>
    <string name="app_name">安天卫士</string>
    <style name="Base.Theme.Demo" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="QuickFunctionCard">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
    </style>
    <style name="QuickFunctionIcon">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">@drawable/circle_background_light_blue</item>
        <item name="android:padding">6dp</item>
        <item name="android:tint">#4285F4</item>
    </style>
    <style name="QuickFunctionLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">vertical</item>
        <item name="android:padding">16dp</item>
    </style>
    <style name="QuickFunctionText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:textColor">#000000</item>
        <item name="android:textSize">12sp</item>
    </style>
    <style name="Theme.About" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_variant</item>
        <item name="android:statusBarColor">@color/primary_variant</item>
    </style>
    <style name="Theme.AntiVirus" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/white</item>
        
        
        <item name="colorSecondary">@color/primary</item>
        <item name="colorSecondaryVariant">@color/primary_variant</item>
        <item name="colorOnSecondary">@color/white</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>

        
        <item name="bottomNavigationStyle">@style/Widget.App.BottomNavigationView</item>
    </style>
    <style name="Theme.Demo" parent="Base.Theme.Demo"/>
    <style name="Widget.App.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemIconTint">@color/bottom_nav_colors</item>
        <item name="itemTextColor">@color/bottom_nav_colors</item>
    </style>
</resources>