package com.antiy.avlsdk;

import static com.antiy.avlsdk.utils.SdkConst.CPU_MONITOR_FILE;
import static com.antiy.avlsdk.utils.SdkConst.CPU_MONITOR_INTERVAL;
import static com.antiy.avlsdk.utils.SdkConst.PC_VIRUS_UPDATE_CHECK_PATH;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.antiy.avlsdk.auth.AuthInfoManager;
import com.antiy.avlsdk.auth.LicenseManager;
import com.antiy.avlsdk.callback.IScanner;
import com.antiy.avlsdk.callback.Logger;
import com.antiy.avlsdk.callback.NetworkTunnel;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.config.Config;
import com.antiy.avlsdk.config.ConfigUpdater;
import com.antiy.avlsdk.entity.InitValue;
import com.antiy.avlsdk.entity.ResultInit;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ResultUpdate;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.entity.ScanMode;
import com.antiy.avlsdk.entity.VirusInfo;
import com.antiy.avlsdk.monitor.CpuUsageMonitor;
import com.antiy.avlsdk.pc.AVLEnginePC;
import com.antiy.avlsdk.pc.AssetsUtil;
import com.antiy.avlsdk.pc.PCEngineInit;
import com.antiy.avlsdk.scan.FileScanner;
import com.antiy.avlsdk.scan.ScannerFactory;
import com.antiy.avlsdk.storage.DataManager;
import com.antiy.avlsdk.update.PCFullUpdateStrategy;
import com.antiy.avlsdk.update.UpdateStrategy;
import com.antiy.avlsdk.update.VirusDatabaseUpdater;
import com.antiy.avlsdk.utils.FileUtil;
import com.antiy.avlsdk.utils.ProcessHelper;
import com.antiy.avlsdk.utils.SdkConst;
import com.antiy.avlsdk.utils.VirusInfoUtil;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * AVL杀毒引擎核心类
 * <p>
 * 该类是整个杀毒引擎的核心控制类,实现了以下主要功能:
 * <ul>
 *   <li>引擎的初始化与资源释放</li>
 *   <li>病毒库的更新与版本管理</li>
 *   <li>文件和目录的病毒扫描</li>
 *   <li>扫描任务的控制(暂停/恢复/停止)</li>
 *   <li>配置管理</li>
 * </ul>
 * <p>
 * 该类采用单例模式设计,确保全局只有一个引擎实例。
 * 使用时需要先调用init()方法进行初始化,然后才能使用其他功能。
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/2
 */
public class AVLEngine {
    private static volatile AVLEngine instance;
    public static Logger Logger;
    private static NetworkTunnel mNetworkTunnel;

    // 替换 ReentrantLock，使用 AtomicBoolean
    private final AtomicBoolean isScanning = new AtomicBoolean(false);

    //加载的配置项
    private Config config;
    private Context mContext;
    private static ResultInit resultInit;

    private IScanner scanner;
    private String PIPE_PATH;

    private AVLEngine() {
    }

    /**
     * 获取AVLEngine的单例实例
     * 使用双重检查锁定模式(Double-Checked Locking)实现单例模式
     * 确保在多线程环境下也只会创建一个实例
     * 
     * @return 返回AVLEngine的唯一实例
     */
    public static AVLEngine getInstance() {
        if (instance == null) {
            synchronized (AVLEngine.class) {
                if (instance == null) {
                    instance = new AVLEngine();
                }
            }
        }
        return instance;
    }

    public Context getContext() {
        return mContext;
    }

    public NetworkTunnel getNetworkManager() {
        return mNetworkTunnel;
    }

    /**
     * @param context 应用上下文
     * @param id      当前车辆的唯一ID
     * @param logger  自定义logger
     * @param tunnel  自定义网络请求
     * @return 初始化结果:原因
     */
    public static ResultInit init(Context context, String id, Logger logger, NetworkTunnel tunnel) {
        AVLEngine engine = getInstance();
        engine.mContext = context.getApplicationContext();
        engine.PIPE_PATH = context.getFilesDir() + File.separator + "pipe";
        Logger = logger;
        Logger.info("start init,param id:" + id);
        mNetworkTunnel = tunnel;
        resultInit = new ResultInit();
        // 手机引擎初始化
        int mobileResult = initMobileEngine(id);
        boolean value = mobileResult == InitValue.ALREADY_INITIALIZED.getCode() || mobileResult == InitValue.SUCCESS.getCode();
        resultInit.isSuccess = value;
        resultInit.reason = value ? "mobile engine success" : "mobile engin init auth fail";
        if (!value) return resultInit;

        if(!BuildConfig.FLAVOR.equals("changan")){
            // 长城版本需要初始化PC引擎
            boolean pcInitResult = initPcEngine();
            resultInit.isSuccess = pcInitResult;
            resultInit.reason = pcInitResult ? "init success" : "pc engine init auth fail";

            prepareUpdateCheckerExe(context);
        }

//        prepareCpuMonitor();
        engine.deletePipeFile();
        AVLCoreEngine.getInstance().installSignalHandler();
        Logger.info("end init result:" + resultInit);
        return resultInit;
    }

    /**
     * 检测更新病毒库的程序
     *
     * @param context
     */
    private static void prepareUpdateCheckerExe(Context context) {
        File virusUpdateFile = new File(context.getFilesDir().getAbsoluteFile() + File.separator + "avl_pc_check_exe");
        String virusUpdatePath = virusUpdateFile.getAbsolutePath();

        // NOTE: if we check the file existence, app update will cause in-compatible `cpu_usage` file
        //     remains on disk, so here we forcibly copy the binary everytime we got inited.
        AssetsUtil.copyAssetFileToFilesDir(context, String.format("%s" + PC_VIRUS_UPDATE_CHECK_PATH,
                        Build.CPU_ABI),
                "/data/vids" + PC_VIRUS_UPDATE_CHECK_PATH);

        if (!virusUpdateFile.canExecute()) {
            virusUpdateFile.setExecutable(true);
        }
    }

    /**
     * 检查更新
     *
     * @return 更新结果
     */
    /**
     * 检查病毒库更新
     * 
     * 该方法会执行以下操作:
     * 1. 通过VirusDatabaseUpdater检查是否有新的病毒库更新
     * 2. 设置30秒超时时间,超时后会取消网络请求
     * 3. 发生错误时会:
     *    - 取消正在进行的网络请求
     *    - 重置PC引擎
     *    - 清理临时文件
     *    - 重新初始化PC引擎
     * 4. 返回更新结果
     *
     * @return ResultUpdate 更新结果对象
     *         - hasUpdate: 是否有更新
     *         - updateSucceeded: 更新是否成功
     */
    public ResultUpdate checkUpdate() {
        ResultUpdate result = VirusDatabaseUpdater.getInstance()
                .checkUpdate()
                .timeout(30, TimeUnit.SECONDS)
                .doOnError(error -> {
                    // 发生错误时（包括超时）取消网络请求
                    mNetworkTunnel.cancelRequest();
                })
                .onErrorReturn(error -> {
                    // 回滚pc和清空临时文件
                    UpdateStrategy strategy = new PCFullUpdateStrategy();
                    strategy.resetPcEngine();
                    strategy.cleanupTempFiles();
                    AVLEnginePC.getInstance().init();

                    ResultUpdate errorResult = new ResultUpdate();
                    errorResult.setHasUpdate(false);
                    errorResult.setUpdateSucceeded(false);
                    return errorResult;
                })
                .blockingGet();
        return result;
    }

    /**
     * 配置加载接口
     *
     * @param config 加载的配置对象
     */
    public void loadConfig(Config config) {
        Logger.info("start loadConfig");
        config.create();
        this.config = config;
        Logger.info("end loadConfig");
    }

    /**
     * 更新配置接口
     * 用于更新单个或多个配置项，不影响其他未指定的配置
     *
     * @param updater 配置更新器
     */
    public void updateConfig(ConfigUpdater updater) {
        Logger.info("start updateConfig");
        if (config == null) {
            Logger.error("Config not initialized");
            return;
        }
        updater.update(config);
        Logger.info("end updateConfig");
    }

    /**
     * 单个文件病毒检测接口（加上返回HASH值，加上是否云查的标记）
     *
     * 该函数用于对指定路径的文件进行病毒扫描，并返回扫描结果。扫描结果包含文件的HASH值以及是否通过云查的标记。
     *
     * @param path 需要进行病毒扫描的文件路径
     * @return ResultScan 返回扫描结果对象，包含文件的HASH值、是否通过云查等信息
     */
    public ResultScan scanFile(String path) {
        Logger.info("start scanFile");
        ResultScan resultScan = new ResultScan();
        if (!resultInit.isSuccess) {
            String error = "Engine not initialized";
            Logger.error(error);
            resultScan.errMsg = error;
            return resultScan;
        }
        resultScan = FileScanner.scan(path);
        Logger.info("end scanFile");
        return resultScan;
    }

    /**
     * 目录正常检测接口（加上数量进度情况）
     *
     * @param path     扫描目录
     * @param listener 扫描过程回调对象
     */
    public void scanDir(String path, ScanListener listener) {
        Logger.info("start scanDir");
        Logger.info("scanDir sdk version:" + getVersion());
        if (!validateScanRequest(path, listener)) {
            return;
        }

        // 使用 compareAndSet 确保原子性操作
        if (!isScanning.compareAndSet(false, true)) {
            String error = "Scanning is in progress, please wait for the task to complete";
            Logger.error(error);
            listener.scanError(ScanErrorType.MULTI_TASK_ERROR);
            Logger.error("Scan already in progress");
            return;
        }

        try {
            if (!initializeScan(path, listener)) {
                return;
            }

            List<File> files = prepareFileList(path);
            if (files == null || files.isEmpty()) {
                Logger.info("No files found in directory");
                notifyScanComplete(listener);
                return;
            }

            // 包装listener以确保状态正确清理
            ScanListener wrappedListener = new ScanListener() {
                @Override
                public void scanFinish() {
                    try {
                        if (listener != null) {
                            listener.scanFinish();
                        }
                    } finally {
                        isScanning.set(false);
                        DataManager.getInstance().maintainDatabase();
                        Logger.info("Scan completed, released lock:" + isScanning.get());
                    }
                }

                // 其他方法直接委托给原始listener
                @Override
                public void scanStart() {
                    if (listener != null) listener.scanStart();
                }

                @Override
                public void scanCount(int count) {
                    if (listener != null) listener.scanCount(count);
                }

                @Override
                public void scanFileStart(int index, String path) {
                    if (listener != null) listener.scanFileStart(index, path);
                }

                @Override
                public void scanFileFinish(int index, String path, ResultScan result) {
                    if (listener != null) listener.scanFileFinish(index, path, result);
                }

                @Override
                public void scanError(ScanErrorType errorMsg) {
                    if (listener != null) listener.scanError(errorMsg);
                }

                @Override
                public void scanStop() {
                    isScanning.set(false);
                    if (listener != null) {
                        listener.scanStop();
                        Logger.info("Scan Stop, released lock:" + isScanning.get());
                    }
                }
            };

            executeScan(files, wrappedListener);
        } catch (Exception e) {
            Logger.error("Error during scan: " + e.getMessage());
            notifyScanError(listener, "Scan failed: " + e.getMessage());
            isScanning.set(false);
            Logger.info("Scan Exception, released lock:" + isScanning.get());
            DataManager.getInstance().maintainDatabase();
        }
    }

    /**
     * 验证扫描请求的有效性
     * 
     * @param path 要扫描的路径
     * @param listener 扫描监听器
     * @return 如果验证通过返回true，否则返回false
     */
    private boolean validateScanRequest(String path, ScanListener listener) {
        // 检查监听器是否为空
        if (listener == null) {
            Logger.error("Listener cannot be null");
            return false;
        }

        // 检查路径是否为空
        if (TextUtils.isEmpty(path)) {
            Logger.error("Path cannot be empty"); 
            notifyScanError(listener, "Invalid path");
            return false;
        }

        // 检查引擎是否已初始化
        if (!resultInit.isSuccess) {
            Logger.error("Engine not initialized");
            notifyScanError(listener, "Engine not initialized"); 
            return false;
        }

        return true;
    }

    /**
     * 初始化扫描操作
     * 
     * @param path 要扫描的目录路径
     * @param listener 扫描监听器
     * @return 如果目录有效返回true，否则返回false
     */
    private boolean initializeScan(String path, ScanListener listener) {
        // 创建File对象检查目录
        File dir = new File(path);
        // 检查目录是否存在且是否为目录
        if (!dir.exists() || !dir.isDirectory()) {
            // 如果路径无效,记录错误日志
            Logger.error("Invalid directory: " + path);
            // 通知监听器扫描错误
            notifyScanError(listener, "Invalid directory");
            return false;
        }
        return true;
    }

    /**
     * 准备要扫描的文件列表
     * 
     * @param path 要扫描的目录路径
     * @return 返回目录下所有文件的列表，如果出错则返回null
     */
    private List<File> prepareFileList(String path) {
        try {
            // 使用FileUtil工具类列出目录下的所有文件
            return FileUtil.listAllFiles(new File(path));
        } catch (Exception e) {
            // 如果列举文件时发生异常，记录错误日志
            Logger.error("Error listing files: " + e.getMessage());
            return null;
        }
    }

    /**
     * 执行扫描操作
     * 
     * @param files 要扫描的文件列表
     * @param listener 扫描监听器
     */
    private void executeScan(List<File> files, ScanListener listener) {
        // 记录找到的文件数量并开始扫描
        Logger.info(files.size() + " files found, scanning will begin");
        
        // 通知监听器扫描开始
        notifyScanStart(listener, files.size());
        
        // 根据文件数量和网络状态决定使用云端扫描还是本地扫描
        ScanMode scanMode = shouldUseCloudScanner(files) ? ScanMode.CLOUD : ScanMode.LOCAL;
        Logger.info("ScanMode:" + scanMode);
        
        // 通过工厂创建对应的扫描器
        scanner = ScannerFactory.createScanner(scanMode,files,listener);
        
        // 启动扫描
        scanner.startScan();
    }

    /**
     * 判断是否应该使用云端扫描器
     * 
     * @param files 要扫描的文件列表
     * @return 如果满足以下条件则返回true:
     *         1. 文件数量超过阈值
     *         2. 网络管理器不为空
     *         3. 网络可用
     */
    private boolean shouldUseCloudScanner(List<File> files) {
        // 获取云端检查阈值
        int threshold = DataManager.getInstance().getCloudCheckThreshold();
        Logger.info("shouldUseCloudScanner CloudCheckThreshold:" + threshold);

        // 判断是否满足云端扫描条件:
        // 1. 文件数量大于阈值
        // 2. 网络管理器存在且网络可用
        boolean isCloudScan = files.size() >  threshold &&
                getNetworkManager() != null &&
                getNetworkManager().isAvailable();
        Logger.info("check is cloud engine:" + isCloudScan);
        return isCloudScan;
    }




    /**
     * 通知扫描开始
     * 该方法用于通知监听器扫描已开始，并告知要扫描的文件总数
     * 
     * @param listener 扫描监听器对象
     * @param fileCount 要扫描的文件总数
     */
    private void notifyScanStart(ScanListener listener, int fileCount) {
        try {
            // 通知扫描开始
            listener.scanStart();
            // 通知要扫描的文件总数
            listener.scanCount(fileCount);
        } catch (Exception e) {
            // 如果通知过程中发生异常，记录错误日志
            Logger.error("Error in scan start notification: " + e.getMessage());
        }
    }

    /**
     * 通知扫描完成
     * 该方法用于通知监听器扫描已完成
     * 如果监听器为空则不执行任何操作
     * 如果通知过程中发生异常会记录错误日志
     * 
     * @param listener 扫描监听器对象
     */
    private void notifyScanComplete(ScanListener listener) {
        if (listener != null) {
            try {
                listener.scanFinish();
            } catch (Exception e) {
                Logger.error("Error in scan complete notification: " + e.getMessage());
            }
        }
    }

    /**
     * 通知扫描错误
     * 该方法用于在扫描出错时通知监听器
     * 会依次调用scanStop()和scanFinish()方法
     * 如果通知过程中发生异常会记录错误日志
     * 
     * @param listener 扫描监听器对象
     * @param error 错误信息
     */
    private void notifyScanError(ScanListener listener, String error) {
        if (listener != null) {
            try {
                // 通知扫描停止
                listener.scanStop();
            } catch (Exception e) {
                Logger.error("Error in scan error notification: " + e.getMessage());
            }
        }
    }

    /**
     * 停止扫描
     * @return true：停止成功，false：停止失败
     */
    public boolean scanStop() {
        Logger.info("start scanStop,scanner = " + scanner);
        if (scanner != null) {
            scanner.stopScan();
            return scanner.isStopped();
        }
        Logger.info("end scanStop");
        return false;
    }

    /**
     * 暂停扫描
     * @return true ：暂停成功，false：暂停失败
     */
    public boolean scanPause() {
        Logger.info("start scanPause");
        if (scanner != null) {
            scanner.pauseScan();
            return scanner.isPaused();
        }
        Logger.info("end scanPause");
        return false;
    }

    /**
     * 恢复扫描，在暂停扫描之后，调用会恢复扫描，默认在扫描的时候调用
     * 是true，则恢复扫描，否则不恢复，
     * @return true ：恢复成功，false：恢复失败
     */
    public boolean scanResume() {
        Logger.info("start scanResume");
        if (scanner != null) {
            scanner.resumeScan();
            return !scanner.isPaused();
        }
        Logger.info("end scanResume");
        return false;
    }

    /**
     * 目录静默检测接口
     *
     * @param path     扫描目录
     * @param listener 扫描过程回调对象
     */
    @SuppressLint("DefaultLocale")
    public void scanDirSilent(String path, ScanListener listener) {
        Logger.info("start scanDirSilent");
        Logger.info("pipe path:" + PIPE_PATH);
        // 检查引擎初始化状态
        if (!resultInit.isSuccess) {
            Logger.error("engine init is fail,scan stop,please init success");
            return;
        }
        // 启动CPU监控进程
        Process cpuMonitorProcess = startCpuMonitor();
        // 包装监听器并开始扫描
        ScanListener wrappedListener = getScanSilentListener(listener, cpuMonitorProcess);
        scanDir(path, wrappedListener);
        
        Logger.info("end scanDirSilent");
    }
    
    /**
     * 启动CPU使用率监控进程
     * @return 启动的进程对象，如果启动失败则返回null
     */
    private Process startCpuMonitor() {
        Process process = null;
        try {
            // 准备CPU监控文件
            String monitorTextPath = mContext.getExternalCacheDir() + CPU_MONITOR_FILE;
            File cpuFile = new File(monitorTextPath);
            if (cpuFile.exists()) {
                cpuFile.delete();
            }
            cpuFile.createNewFile();
            
            // 检查CPU监控路径是否有效
            if (TextUtils.isEmpty(CpuUsageMonitor.getCpuUsagePath())) {
                Logger.error("CPU usage monitor path is empty");
                return null;
            }
            
            // 构建并执行命令
            AVLEngine.Logger.info("cpu limit percent:" + DataManager.getInstance().getSilentPerformanceThreshold());
            @SuppressLint("DefaultLocale") String command = String.format("%s -p %d -P %d -i %d -f %s --pipe %s",
                    CpuUsageMonitor.getCpuUsagePath(),
                    android.os.Process.myPid(),
                    DataManager.getInstance().getSilentPerformanceThreshold(),
                    CPU_MONITOR_INTERVAL,
                    monitorTextPath,
                    PIPE_PATH);
            Logger.debug("command:" + command);
            
            process = Runtime.getRuntime().exec(command);
            Logger.info("cpu monitor process state:" + process.isAlive());
        } catch (IOException e) {
            Logger.error("Failed to start CPU monitor: " + e.getMessage());
            e.printStackTrace();
        }
        return process;
    }

    /**
     * 获取扫描监听器
     * @param listener 扫描监听器对象
     * @param process 进程对象
     * @return 扫描监听器对象
     */
    private @NonNull ScanListener getScanSilentListener(ScanListener listener, Process process) {
        return new ScanListener() {

            @Override
            public void scanStart() {
                if (listener != null) listener.scanStart();
            }

            @Override
            public void scanStop() {
                if (listener != null) listener.scanStop();
            }

            @Override
            public void scanFinish() {
                if (listener != null) {
                    terminateProcess(process);
                    listener.scanFinish();
                }
            }

            @Override
            public void scanCount(int count) {
                if (listener != null) listener.scanCount(count);
            }

            @Override
            public void scanFileStart(int index, String path) {
                if (listener != null) listener.scanFileStart(index, path);
            }

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {
                if (listener != null) listener.scanFileFinish(index, path, result);
            }

            @Override
            public void scanError(ScanErrorType errorMsg) {
                if (listener != null) listener.scanError(errorMsg);
            }
        };
    }

    private void terminateProcess(Process process) {
        if (process != null && process.isAlive()) {
            Logger.debug("cpu monitor:" + String.format("Sending signal to %d of signal SIG_USR1", ProcessHelper.getPid(process)));

            try {
                Runtime.getRuntime().exec(new String[]{"kill", "-SIGUSER", String.format("%d", ProcessHelper.getPid(process))});
            } catch (IOException e) {
                e.printStackTrace();
            }

            try {
                process.waitFor(100, TimeUnit.MILLISECONDS);
                if (process.isAlive()) {
                    process.destroyForcibly();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除管道文件
     */
    private void deletePipeFile() {
        File file = new File(PIPE_PATH);
        if (file.exists()) {
            Logger.debug("delete pipe file");
            file.delete();
        }
    }

    /**
     * 病毒库版本号获取接口
     * 获取当前加载的病毒库的版本号。此信息随病毒库更新而改变
     *
     * @return 当前病毒库的版本号 如 20240811aa_20240822
     */
    public static String getVdbVersion() {
        String vdbVersion = AVLCoreEngine.getInstance().getSigLibVersion() + "_" + AVLEnginePC.getInstance().getDbInfo();
        Logger.info("start getVdbVersion：" + vdbVersion);
        return vdbVersion;
    }

    /**
     * 引擎版本号获取接口
     *
     * @return 当前引擎的版本号 如 1.0
     */
    public static String getVersion() {
        String version = SdkConst.SDV_VERSION;
        Logger.info("start getVersion ：" + version);
        return version;
    }

    /**
     * 初始化手机杀毒引擎
     *
     * @return true: 初始化成功; false: 初始化失败
     */
    private static int initMobileEngine(String uuid) {
        //移动引擎初始化
        AVLCoreEngine.getInstance().prepareData(getInstance().getContext());

        AuthInfoManager info = AuthInfoManager.getInstance(getInstance().getContext());
        String authToken = info.getToken();
        String alreadyUUID = info.getUuid();
        String clientId = info.getClientId();
        Logger.info("authToken=" + authToken + ",alreadyUUID=" + alreadyUUID + ",clientId=" + clientId);
        AVLCoreEngine.getInstance().setupAuthParams(alreadyUUID, clientId);

        int mobileInitResult = AVLCoreEngine.getInstance().init(
                AVLCoreEngine.getInstance().getLibPath(),
                AVLCoreEngine.getInstance().getDbPath(),
                authToken
        );

        InitValue result = InitValue.fromCode(mobileInitResult);
        AVLEngine.Logger.error("mobile:" + result.getCode() + ":" + result.getDescription() +
                ",version:" + AVLCoreEngine.getInstance().getSigLibVersion());
        // 每次请求完后进行更新token
        new Thread(() -> instance.requestAuthToken(uuid)).start();

        return mobileInitResult;
    }

    /**
     * 请求认证令牌
     * 本方法通过LicenseManager请求新的认证令牌，并从SharedPreferences中获取最新的认证令牌
     * 认证令牌用于验证和授权，确保应用的合法使用
     */
    private void requestAuthToken(String uuid) {
        // 创建LicenseManager实例，用于管理许可证和认证令牌
        LicenseManager licenseManager = new LicenseManager(getInstance().getContext());
        // 请求新的认证令牌
        licenseManager.requestAuthToken(uuid);

    }


    /**
     * 初始化PC杀毒引擎
     * <p>
     * 该方法负责初始化PC端的杀毒引擎，通过调用AVLEnginePC的实例法进引的初始化准备和实际初始化
     * 初始化结果将决定是否能够成功使用PC杀毒引擎进行后续的杀毒操作
     *
     * @return boolean 表示PC杀毒引擎初始化是否成功 true表示成功，false表示失败
     */
    private static boolean initPcEngine() {
        //PC引擎初始化
        AVLEnginePC.getInstance().prepareData(getInstance().getContext());
        int pcInitResult = AVLEnginePC.getInstance().init();
        AVLEngine.Logger.error("pc:" + pcInitResult +"," + PCEngineInit.get(pcInitResult).getMessage()
                + ",版本号:" + AVLEnginePC.getInstance().getDbInfo());
        return pcInitResult == 0;
    }

    /**
     * 初始化cpu程序
     */
    private static void prepareCpuMonitor() {
        String VIDS_SDK_PATH = "/data/vids" + "/cpu_usage";

        CpuUsageMonitor.copyCpuUsageBinary(getInstance().getContext(),VIDS_SDK_PATH);
    }


    /**
     * 释放引擎资源
     * 该方法用于释放移动端和PC端杀毒引擎占用的资源
     * 包括:
     * 1. 释放移动端引擎资源
     * 2. 卸载PC端引擎
     * 
     * 建议在不再使用引擎时调用此方法进行资源释放
     */
    public void release() {
        AVLCoreEngine.getInstance().release();
        AVLEnginePC.getInstance().unloadEngine();
    }


    /**
     * 获取引擎初始化结果
     * 该方法返回引擎初始化后的结果对象，包含初始化是否成功及原因等信息
     * 
     * @return ResultInit 初始化结果对象
     *         - isSuccess: 初始化是否成功
     *         - reason: 初始化结果原因说明
     */
    public ResultInit getInitResult() {
        return resultInit;
    }

    /**
     * 获取病毒描述
     * @param virusName
     * @return
     */
    public VirusInfo getVirusInfo(String virusName){
        return VirusInfoUtil.parseVirusName(virusName);
    }

    /**
     *
     * @param fileDescriptor 目标文件描述符，从ContentURLProvider中获取
     */
    public ResultScan scanWithFd(int fileDescriptor) {
        Logger.info("start scanWithFd");
        ResultScan resultScan = new ResultScan();
        if (!resultInit.isSuccess) {
            String error = "Engine not initialized";
            Logger.error(error);
            resultScan.errMsg = error;
            return resultScan;
        }
        String virusName = AVLCoreEngine.getInstance().scan(fileDescriptor);
        resultScan = new ResultScan();
        resultScan.isCloudScan = false;
        resultScan.virusName = virusName;
        resultScan.isMalicious = !TextUtils.isEmpty(virusName);
        Logger.info("scanWithFd result:" + virusName);
        return resultScan;
    }
}
