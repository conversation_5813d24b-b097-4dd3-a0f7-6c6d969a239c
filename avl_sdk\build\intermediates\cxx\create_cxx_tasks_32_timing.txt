# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 61ms
create_cxx_tasks completed in 62ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 62ms
create_cxx_tasks completed in 64ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 85ms
create_cxx_tasks completed in 86ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 16ms
    create-module-model completed in 16ms
    create-ARM64_V8A-model 110ms
    create-X86_64-model 98ms
    create-module-model 18ms
    create-ARMEABI_V7A-model 10ms
    create-X86_64-model 47ms
    create-ARM64_V8A-model 16ms
    create-X86_64-model 16ms
    [gap of 21ms]
    create-ARM64_V8A-model 10ms
    create-module-model 16ms
    create-module-model 16ms
    create-ARM64_V8A-model 32ms
    create-X86-model 16ms
    [gap of 15ms]
  create-initial-cxx-model completed in 459ms
  [gap of 16ms]
create_cxx_tasks completed in 475ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 66ms
create_cxx_tasks completed in 67ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 68ms
create_cxx_tasks completed in 69ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 69ms]
    create-ARM64_V8A-model 15ms
    [gap of 53ms]
  create-initial-cxx-model completed in 137ms
create_cxx_tasks completed in 140ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 119ms
create_cxx_tasks completed in 123ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 99ms]
    create-variant-model 28ms
    [gap of 10ms]
  create-initial-cxx-model completed in 137ms
create_cxx_tasks completed in 138ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 62ms
create_cxx_tasks completed in 63ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 66ms
create_cxx_tasks completed in 67ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 54ms
create_cxx_tasks completed in 58ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 69ms
create_cxx_tasks completed in 70ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 95ms]
    create-module-model 10ms
    [gap of 40ms]
  create-initial-cxx-model completed in 145ms
create_cxx_tasks completed in 153ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 74ms
create_cxx_tasks completed in 75ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 78ms
create_cxx_tasks completed in 79ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 67ms
create_cxx_tasks completed in 71ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 99ms
create_cxx_tasks completed in 101ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 69ms
create_cxx_tasks completed in 71ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 60ms
create_cxx_tasks completed in 64ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 157ms
  [gap of 33ms]
create_cxx_tasks completed in 190ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 63ms
create_cxx_tasks completed in 64ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 57ms
create_cxx_tasks completed in 58ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 60ms
create_cxx_tasks completed in 62ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 70ms
create_cxx_tasks completed in 71ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 69ms
create_cxx_tasks completed in 70ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 67ms
create_cxx_tasks completed in 68ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 74ms
create_cxx_tasks completed in 75ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 141ms
create_cxx_tasks completed in 144ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 68ms
create_cxx_tasks completed in 69ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 99ms
create_cxx_tasks completed in 101ms

