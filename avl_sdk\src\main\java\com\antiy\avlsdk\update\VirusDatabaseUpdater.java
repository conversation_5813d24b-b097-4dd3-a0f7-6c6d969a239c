package com.antiy.avlsdk.update;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.DownloadCallback;
import com.antiy.avlsdk.entity.ResultUpdate;
import com.antiy.avlsdk.entity.VirusUpdateEntity;
import com.antiy.avlsdk.storage.EncryptorHelper;
import com.antiy.avlsdk.utils.JsonUtils;
import com.antiy.avlsdk.utils.SdkConst;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;

import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * Author: wangbiao
 * Date: 2024/10/11 17:11
 * Description:
 */
public class VirusDatabaseUpdater {
    private static volatile VirusDatabaseUpdater instance;
    private String DOWNLOAD_PREFIX = "cccj/update/";
    private VirusDatabaseUpdater() {}

    public static synchronized VirusDatabaseUpdater getInstance() {
        if (instance == null) {
            instance = new VirusDatabaseUpdater();
        }
        return instance;
    }

    public Single<ResultUpdate> checkUpdate() {
        return Single.create(emitter -> {
            String confUrl = DOWNLOAD_PREFIX + AVLEngine.getVdbVersion() + ".conf";

            // 下载配置文件
            AVLEngine.getInstance().getNetworkManager().download(
                confUrl,
                new DownloadCallback(){
                    @Override
                    public void onError(String msg) {
                        if (!emitter.isDisposed()) {
                            emitter.onError(new Exception(msg));
                        }
                    }

                    @Override
                    public void onFinish(int code, String responseFileName, String localFilePath) {
                        AVLEngine.Logger.info("conf local path：" + localFilePath);
                        if (!emitter.isDisposed()) {
                            // 读取下载的配置文件
                            try {
                                StringBuilder content = new StringBuilder();
                                try (BufferedReader reader = new BufferedReader(new FileReader(localFilePath))) {
                                    String line;
                                    while ((line = reader.readLine()) != null) {
                                        content.append(line);
                                    }
                                }
                                emitter.onSuccess(content.toString());
                            } catch (Exception e) {
                                emitter.onError(e);
                            }
                        }
                    }
                }
            );
        })
        .map(responseBody -> {
            return JsonUtils.toObject(responseBody.toString(), VirusUpdateEntity.class);
        })
        .flatMap(virusUpdate -> {
            String updateVersion = virusUpdate.getVersion();
            String currentVersion = AVLEngine.getVdbVersion();
            AVLEngine.Logger.info("updateVersion:" + updateVersion + ",currentVersion:" + currentVersion);
            if (!updateVersion.equals(currentVersion)) {
                return downloadAndUpdate(virusUpdate);
            } else {
                ResultUpdate result = new ResultUpdate();
                result.setHasUpdate(false);
                result.setUpdateSucceeded(true);
                return Single.just(result);
            }
        })
        .subscribeOn(Schedulers.io())
        .doOnError(error -> 
            AVLEngine.Logger.error("Update check failed: " + error.getMessage())
        );
    }

    private Single<ResultUpdate> downloadAndUpdate(VirusUpdateEntity virusUpdate) {
        String downloadUrl = virusUpdate.getDownloadUrl();
        
        return Single.<ResultUpdate>create(emitter -> {
            AVLEngine.getInstance().getNetworkManager().download(
                downloadUrl,
                new DownloadCallback() {
                    @Override
                    public void onFinish(int code, String responseFileName, String localFilePath) {
                        if (!emitter.isDisposed()) {
                            try {
                                // 计算下载文件的 SHA-256 哈希值
                                String fileHash = EncryptorHelper.calcPathSHA256(localFilePath);
                                AVLEngine.Logger.info("update virus package sha256 hash: " + fileHash);
                                // 比对哈希值
                                if (!fileHash.equals(virusUpdate.getHash())) {
                                    AVLEngine.Logger.info("File hash check failed");
                                    // 删除损坏的文件
//                                    new File(localFilePath).delete();
                                    emitter.onSuccess(new ResultUpdate(true,false));
                                    return;
                                }
                                long startTime = System.currentTimeMillis();
                                // 文件完整性验证通过，继续更新流程
                                String targetDir = AVLEngine.getInstance().getContext().getFilesDir() + SdkConst.TARGET_ZIP_PATH;
                                UpdateCoordinator coordinator = new UpdateCoordinator();
                                ResultUpdate result = coordinator.handleUpdate(virusUpdate, localFilePath, targetDir);
                                long endTime = System.currentTimeMillis();
                                AVLEngine.Logger.info("Update replace file time: " + (endTime - startTime) + "ms");
                                // 更新成功后删除conf文件
                                if (result.isUpdateSucceeded()) {
                                    emitter.onSuccess(result);
                                }else {
                                    emitter.onError(new Exception("update fail"));
                                }
                                new File(localFilePath).delete();
                            } catch (Exception e) {
                                emitter.onError(e);
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (!emitter.isDisposed()) {
                            emitter.onError(new Exception(msg));
                        }
                    }
                }
            );
        })
        .subscribeOn(Schedulers.io())
        .doOnError(error -> 
            AVLEngine.Logger.error("Download and update failed: " + error.getMessage())
        );
    }

    public void update(String path){
        VirusUpdateEntity entity = new VirusUpdateEntity();
        entity.setDownloadUrl(path);
        entity.setIstotal("2_2");
        int start = path.lastIndexOf("/") + 1;
        int end = path.lastIndexOf(".");
        entity.setVersion(path.substring(start,end));
        AVLEngine.Logger.error(entity.toString());
        String targetDir = AVLEngine.getInstance().getContext().getFilesDir() + SdkConst.TARGET_ZIP_PATH;
        AVLEngine.Logger.info("targetDir:" + targetDir + ",localFilePath:" + path);
        UpdateCoordinator coordinator = new UpdateCoordinator();
        ResultUpdate update = coordinator.handleUpdate(entity,path,targetDir);
        AVLEngine.Logger.error("update fail: " + update);
    }
}
