// Generated by view binder compiler. Do not edit!
package com.antiy.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.antiy.demo.R;
import com.google.android.material.radiobutton.MaterialRadioButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddBlackWhiteItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText etHash;

  @NonNull
  public final MaterialRadioButton radioBlack;

  @NonNull
  public final RadioGroup radioGroupType;

  @NonNull
  public final MaterialRadioButton radioWhite;

  @NonNull
  public final TextView tvTypeLabel;

  private DialogAddBlackWhiteItemBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText etHash, @NonNull MaterialRadioButton radioBlack,
      @NonNull RadioGroup radioGroupType, @NonNull MaterialRadioButton radioWhite,
      @NonNull TextView tvTypeLabel) {
    this.rootView = rootView;
    this.etHash = etHash;
    this.radioBlack = radioBlack;
    this.radioGroupType = radioGroupType;
    this.radioWhite = radioWhite;
    this.tvTypeLabel = tvTypeLabel;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddBlackWhiteItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddBlackWhiteItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_black_white_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddBlackWhiteItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.etHash;
      TextInputEditText etHash = ViewBindings.findChildViewById(rootView, id);
      if (etHash == null) {
        break missingId;
      }

      id = R.id.radioBlack;
      MaterialRadioButton radioBlack = ViewBindings.findChildViewById(rootView, id);
      if (radioBlack == null) {
        break missingId;
      }

      id = R.id.radioGroupType;
      RadioGroup radioGroupType = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupType == null) {
        break missingId;
      }

      id = R.id.radioWhite;
      MaterialRadioButton radioWhite = ViewBindings.findChildViewById(rootView, id);
      if (radioWhite == null) {
        break missingId;
      }

      id = R.id.tvTypeLabel;
      TextView tvTypeLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvTypeLabel == null) {
        break missingId;
      }

      return new DialogAddBlackWhiteItemBinding((LinearLayout) rootView, etHash, radioBlack,
          radioGroupType, radioWhite, tvTypeLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
