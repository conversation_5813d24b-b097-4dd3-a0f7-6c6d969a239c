commit f0917801827171cef9623e6b2949bed07a869631
Author: wa<PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Mon May 26 15:31:07 2025 +0800

    refactor(avl_sdk): 重构文件类型识别逻辑
    
    - 新增 ArchiveIdentifier 类，专门用于识别归档文件类型
    - 在 FileChecker 中使用 ArchiveIdentifier 代替原有的文件类型识别逻辑
    - 优化了 APK 和 JAR 文件的识别过程，提高了代码可读性和可维护性

commit 41fbc75258d9b94fd3e37afef33b16bb34c0fa3a
Author: wang<PERSON><PERSON> <<EMAIL>>
Date:   Thu May 22 17:33:18 2025 +0800

    fix(avl_sdk): 修复云查杀停止响应问题
    
    -优化任务队列处理逻辑，确保所有任务都能被正确处理
    - 添加日志记录，帮助分析扫描进度和状态
    - 修复本地扫描完成后未发送广播的问题
    - 优化线程池关闭逻辑，确保及时释放资源

commit bcceba8e61dd97d30b4bd2c88c54e3472c7e3b74
Author: wang<PERSON><PERSON> <<EMAIL>>
Date:   Thu May 22 17:32:33 2025 +0800

    feat(ScanFragment): 添加文件扫描功能
    
    - 在 fragment_scan.xml 中添加文件扫描相关的 UI 元素
    - 在 ScanFragment.kt 中实现文件选择和扫描逻辑
    - 使用协程进行异步扫描，展示扫描结果

commit ad7331237110b3be51abbaefb8659ae055f2845f
Author: wangbiao <<EMAIL>>
Date:   Thu May 22 17:01:30 2025 +0800

    refactor(app): 优化云查杀大小阈值配置并完善网络请求处理
    
    - 在 HomeActivity 和 MainActivity 中添加云查杀大小阈值的共享偏好设置
    - 使用共享偏好设置中的值来配置 AVLEngine 的云查杀大小阈值
    - 在 NetworkManager 中改进 onResponse 方法，对响应码进行判断处理
    - 在 ScanningActivity 中添加扫描日志输出，用于调试和监控扫描过程

commit 6f57710fd8a600f0ebe621d657270910b8fa4243
Author: wangbiao <<EMAIL>>
Date:   Wed May 21 18:45:57 2025 +0800

    feat(cloud_scan): 添加云查阈值设置功能
    
    - 在 CloudScanSettingsActivity 中添加云查阈

commit f29330e0f4f41a3fa7ecc8521e6d73f1d0a40e3a
Author: wangbiao <<EMAIL>>
Date:   Wed May 21 16:09:43 2025 +0800

    refactor(avl_sdk): 将日志级别从 debug 改为 info
    
    - 修改了多个文件中的日志级别，包括 CloudScanService、DatabaseHelper、FileChecker、LocalScanner 等
    - 将 debug 日志级别改为 info，以减少日志输出量
    -此修改统一了日志级别，提高了代码的一致性和可维护性

commit 38292c2c6d5bc6b98abfeea7f7cfed108416b080
Author: Zhang Yueqian <<EMAIL>>
Date:   Wed May 21 10:53:30 2025 +0800

    fix: 防止引擎加载异常。

commit 318bfd99c62308b1342ba76c05f346bec6ad5165
Author: wangbiao <<EMAIL>>
Date:   Thu May 15 16:19:19 2025 +0800

    fix(avl_sdk): 给 PC 端升级检查文件添加可执行权限
    
    - 新增 PCConstant 类，定义厂商目录常量- 在 UpdateStrategy 类中添加代码，为升级检查文件设置可执行权限
    - 优化日志输出，记录权限设置结果

commit 8ce2d31d5350e6e3c3746364093089a2511b2d49
Author: wangbiao <<EMAIL>>
Date:   Thu May 15 15:34:19 2025 +0800

    refactor(avl_sdk): 删除内存监控工具并优化 PC 病毒更新检查路径
    
    - 删除了 MemoryMonitor 类，移除了不必要的内存监控功能
    - 优化了 pcVirusUpdateCheck 方法中的可执行文件路径获取逻辑

commit 46444361e109e23eafe34435c66a13a81cd463a6
Author: wangbiao <<EMAIL>>
Date:   Wed May 14 15:18:14 2025 +0800

    fix(avl_sdk): 更新白名单时清除缓存数据
    
    - 在 Config 类中，更新白名单后增加清除缓存数据的操作
    - 在 DataManager 类中添加 clearCacheData 方法，用于清除所有缓存数据

commit 627e3bf08b91c17fbd9b19c8afba2f3e2b942578
Author: wangbiao <<EMAIL>>
Date:   Wed May 14 12:03:12 2025 +0800

    refactor(build.gradle): 将 productFlavors 中的 main 修改为 base
    
    修改了以下文件中的 productFlavors：
    - app/build.gradle
    -avl_pc/build.gradle
    -avl_sdk/build.gradle
    
    将 main 抛 flavor 修改为 base，以更好地适应多 flavor 管理的需求。

commit d409308a2c3d35e2e5f1ebef28ef9528dc6e4861
Author: wangbiao <<EMAIL>>
Date:   Wed May 14 12:02:37 2025 +0800

    feat:删除多余的病毒库更新包

commit 18e19503955938e0642bfc610fe8eed3eec5dca6
Author: wangbiao <<EMAIL>>
Date:   Wed May 14 11:58:57 2025 +0800

    refactor(avl_monitor): 重构 CpuUsageMonitor 类
    
    - 修改 init 方法名称为 copyCpuUsageBinary，更准确地反映该方法的功能
    - 为 copyCpuUsageBinary 方法添加 destinationPath 参数，使方法更具可配置性
    - 移除方法内部冗余的代码和不必要的注释
    -优化文件复制逻辑，使用传入的 destinationPath 参数

commit a528de51e36feaabe548fedc79ecda3524e22de0
Author: wangbiao <<EMAIL>>
Date:   Wed May 14 11:15:32 2025 +0800

    refactor(avl_pc): 重构 VendorZipUtils 类
    
    - 移除了未使用的 Context 参数
    - 使用 java.nio.file.Files 替代 java.io.FileInputStream
    - 优化了 unzipFromVendor 方法的参数，使其更灵活
    - 增加了日志输出，方便调试

commit 60b8fec479de053a3ec169e68457587bec6b1107
Author: wangbiao <<EMAIL>>
Date:   Wed May 14 10:47:34 2025 +0800

    feat(avl_pc): 优化资产工具类功能
    
    - 重构 unzipFromAssets 方法，增加目标目录参数
    - 添加日志输出，便于调试和跟踪
    - 新增 isFolderHasFiles 方法，用于检查文件夹是否包含文件
    - 优化代码结构，提高可读性和可维护性

commit b833e7578513b893d53e85308c7a63c04f5cac3e
Author: wangbiao <<EMAIL>>
Date:   Wed May 14 10:24:46 2025 +0800

    refactor(avl_sdk): 优化代码结构和注释
    
    -移除了未使用的导入和变量- 调整了代码结构，提高了代码的可读性- 更新了注释，明确了函数参数的含义

commit 0bec6d3174bdcb67a2b1f15d3afc82cfea33683f
Author: wangbiao <<EMAIL>>
Date:   Tue May 13 11:40:21 2025 +0800

    feat(app): 添加内容URI处理功能
    
    - 在 AndroidManifest.xml 中添加了处理内容 URI 的 intent 过滤器
    - 在 HomeActivity 中实现了处理内容 URI 的函数 handUri
    - 优化了 AVLEngine 初始化和配置加载的逻辑
    - 更新了权限请求和处理流程

commit e5b7bcb341acb06a1bdb7b32565a01ee4b0686c0
Author: wangbiao <<EMAIL>>
Date:   Tue May 13 11:39:03 2025 +0800

    feat(app): 更新压缩包的libavlm.so.

commit 19dbe461296e02bb8268c51a666c4ce8d0a6b530
Author: wangbiao <<EMAIL>>
Date:   Tue May 13 10:04:27 2025 +0800

    feat(app): 添加清空缓存功能并优化日志输出
    
    - 在设置页面添加清空缓存功能，清除扫描结果缓存- 优化日志输出，增加calcPathSHA256和黑名单匹配的日志信息
    - 修复设置页面布局，调整主题颜色

commit 59c84e7c4c948421dabbbcc038956e1818cef038
Author: wangbiao <<EMAIL>>
Date:   Tue May 13 09:30:30 2025 +0800

    refactor(avl_sdk): 优化黑白名单匹配逻辑
    
    - 将 BlackWhiteManager 中的 match 方法参数从路径改为 hash 值- 更新 DatabaseHelper 中的 matchBlackWhiteData 方法参数名
    - 修改 FileScanner 中的黑白名单匹配逻辑，先计算文件 SHA256 哈希值
    -优化 LocalScanner 中的扫描停止处理

commit d71f8d272f231377c71c4daa76ccf2344f20e106
Author: Zhang Yueqian <<EMAIL>>
Date:   Fri May 9 10:13:31 2025 +0800

    fix: 避免泄露栈内存

commit ecfc8b3a2930f472e853f59153c42afaa8288662
Author: wangbiao <<EMAIL>>
Date:   Thu May 8 17:24:43 2025 +0800

    feat：更新病毒库和引擎包

commit e4b6428df546637eeba6441956fac6feee1b32ab
Author: Zhang Yueqian <<EMAIL>>
Date:   Thu May 8 17:20:28 2025 +0800

    feat[WIP]: 测试scanFd是否可行

commit 9423d8fb2822a55ac8418835d030d7c1ab589953
Author: wangbiao <<EMAIL>>
Date:   Thu May 8 17:08:16 2025 +0800

    feat：添加病毒库和引擎包

commit 02e6f5ae2cce86e728fd953813268a20d3acc1e2
Author: Zhang Yueqian <<EMAIL>>
Date:   Thu May 8 10:51:31 2025 +0800

    feat[WIP]: 修改方法签名，文件大小是long型。

commit 7b1994c5edd9a8300ff2f5aaf9bfa0c3146f51c0
Author: Zhang Yueqian <<EMAIL>>
Date:   Thu May 8 10:38:46 2025 +0800

    feat[WIP]: 新增一个scan方法，支持传入fd,filesize来扫描。

commit cba3215c0ae6e57e376644cd4ee051a0bcc6ee4b
Author: wangbiao <<EMAIL>>
Date:   Wed May 7 14:50:58 2025 +0800

    refactor(avl): 重构 AVLEngine 初始化逻辑并优化首页显示
    
    - 修改 isAVLEngineInitialized 函数，使用 initResult 替代 getInitResult()
    - 新增 isFolderHasFiles 工具函数
    - 在 HomeActivity 中初始化成功后更新引擎状态
    - 在 HomeFragment 中添加 updateEngineStatus 函数用于更新状态显示



