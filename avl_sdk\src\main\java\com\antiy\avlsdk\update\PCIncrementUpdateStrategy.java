package com.antiy.avlsdk.update;

import static com.antiy.avlsdk.utils.SdkConst.DIFF_TEXT;

import android.os.Build;
import android.util.Pair;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.pc.AVLEnginePC;
import com.antiy.avlsdk.utils.FileUtil;
import com.antiy.avlsdk.utils.SdkConst;
import com.antiy.avlsdk.utils.ZipExtractor;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

/**
 * Author: wangbiao
 * Date: 2024/10/11 17:09
 * Description:
 */
public class PCIncrementUpdateStrategy extends UpdateStrategy{

    @Override
    public boolean performUpdate(String pcPackagePath, Pair<UpdateTypeEnum, UpdateTypeEnum> pair) {
        AVLEngine.Logger.info("Virus library path:" + pcPackagePath);
        String pcEngineDirectory = AVLEnginePC.getInstance().getAVLPCPath();
        AVLEngine.Logger.error("Update the virus database version before updating:" + AVLEnginePC.getInstance().getDbInfo());
        AVLEnginePC.getInstance().unloadEngine();
        // 1.备份PC病毒库
        backupPCEngine();

        // 2. 解压PC病毒库压缩包
        AVLEngine.Logger.info("Start decompressing PC virus library");
        try {
            ZipExtractor extractor = new ZipExtractor();
            extractor.extractTarContent(pcPackagePath, new File(pcPackagePath).getParent());
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }

        AVLEngine.Logger.info("Successfully decompressed PC virus database");
        incrementalUpdate(new File(pcPackagePath).getParent(), pcEngineDirectory);

        // 检查pc的更新库是否OK
        boolean pcResult = pcVirusUpdateCheck();
        if (!pcResult) {
            AVLEngine.Logger.info("Failed to update PC Increment package through binary detection");
//            resetPcEngine();
            return false;
        }

        boolean result = AVLEnginePC.getInstance().init() == 0;
        AVLEngine.Logger.info("Updated virus library version:" + AVLEnginePC.getInstance().getDbInfo());
        if (result) {
            // 3. 删除备份的文件和解压文件
//            cleanupTempFiles();
        }
        /*else {
            resetPcEngine();
        }*/
        return result;
    }

    protected String backupPCEngine() {
        String pcEngineDirectory = AVLEnginePC.getInstance().getAVLPCPath();
        String backupDirectory = pcEngineDirectory + SdkConst.AVL_PC_BACKUP_SUFFIX;
        AVLEngine.Logger.info("Start backing up PC virus database,path:" + backupDirectory);
        try {
            // 使用 Files.copy() 进行递归复制
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Files.createDirectories(Paths.get(backupDirectory)); // 创建备份目录
                Files.walk(Paths.get(pcEngineDirectory)).forEach(sourcePath -> {
                    Path targetPath = Paths.get(backupDirectory, sourcePath.toString().substring(pcEngineDirectory.length()));
                    try {
                        if (Files.isDirectory(sourcePath)) {
                            Files.createDirectories(targetPath); // 创建目标目录
                        } else {
                            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING); // 复制文件
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return backupDirectory;
    }


    public void incrementalUpdate(String zipBasePath, String targetDirectory) {
        // 1. 读取diff.txt文件
        List<String> diffLines = readDiff(zipBasePath);

        // 2. 处理增量更新
        for (String line : diffLines) {
            if (line.startsWith("+")) {
                String filePath = line.substring(1).trim();
                // 处理新增或修改文件
                String sourcePath = zipBasePath + File.separator + filePath;
                String targetPath = targetDirectory + File.separator + filePath;
                FileUtil.replaceFile(sourcePath, targetPath);
            } else if (line.startsWith("-")) {
                String filePath = line.substring(1).trim();
                // 删除文件
                FileUtil.deleteFile(new File(targetDirectory, filePath));
            }
        }
    }

    /**
     * 读取压缩包里的diff.txt文件
     */
    private List<String> readDiff(String zipBasePath) {
        // 1. 读取diff.txt文件
        List<String> diffLines = new ArrayList<>();
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            try {
                diffLines = Files.readAllLines(Paths.get(zipBasePath + DIFF_TEXT));
            } catch (IOException e) {
                AVLEngine.Logger.error("it's not found diff.txt file");
                e.printStackTrace();
            }
            AVLEngine.Logger.info("diff文件内容:" + diffLines);
        }
        return diffLines;

    }

}
