apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'


android {
    namespace 'com.antiy.demo'
    compileSdk 34

    defaultConfig {
        applicationId "com.antiy.demo"
        minSdk 26
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    flavorDimensions "client"
    productFlavors {
        base {
            dimension "client"
        }
        greatwall {
            dimension "client"
        }
        changan {
            dimension "client"
        }
    }

    signingConfigs {
        all {
            keyAlias 'avlsdk'
            keyPassword 'N7oegv&x%xUW!w'
            storePassword 'N7oegv&x%xUW!w'
            storeFile file('../avlsdk.keystore')
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation "androidx.core:core-ktx:1.1.0"
    implementation "androidx.appcompat:appcompat:1.4.2"
    implementation "com.google.android.material:material:1.6.0"
    implementation "androidx.activity:activity:1.1.1"
    implementation "androidx.constraintlayout:constraintlayout:1.1.3"
    testImplementation "junit:junit:4.13.2"
    androidTestImplementation "androidx.test.ext:junit:1.2.1"
    androidTestImplementation "androidx.test.espresso:espresso-core:3.6.1"
    implementation project(":avl_sdk")
    implementation "com.squareup.okhttp3:okhttp:4.9.0"
    implementation "com.squareup.okhttp3:logging-interceptor:4.9.0"
    
    // Kotlin协程依赖
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"
    
//    debugImplementation 'com.guolindev.glance:glance:1.1.0'
}
