package com.antiy.avlsdk.config;

import com.antiy.avlsdk.entity.ScanFileType;
import com.antiy.avlsdk.storage.DataManager;
import com.antiy.avlsdk.storage.DatabaseHelper;

import java.util.HashMap;
import java.util.List;

/**
 * Copyright (C), 2020-2024
 * FileName: Config
 * Author: wangbiao
 * Date: 2024/9/2 10:29
 * Description: 配置管理类
 * 
 * 该类采用Builder设计模式实现配置的构建和管理，主要功能包括：
 * 1. 管理黑白名单配置
 * 2. 管理文件扫描类型配置
 * 3. 管理云查询相关阈值配置
 * 4. 管理静默查询阈值配置
 * 5. 管理历史缓存相关配置
 * 
 * 配置数据通过SharedPreferences持久化存储，支持动态更新各项配置
 */
public class Config {

    /** 
     * 黑白名单表
     * Key: 文件hash值
     * Value: true表示白名单，false表示黑名单
     */
    private HashMap<String,Boolean> blackWhiteMap;
    
    /** 
     * 支持扫描的文件类型列表
     * 包含所有需要进行病毒扫描的文件类型
     */
    private List<ScanFileType> scanTypes;
    
    /** 
     * 云查询文件数量阈值
     * 当待扫描文件数量超过此阈值时触发云查询
     */
    private int cloudThreshold;
    
    /** 
     * 云查询文件大小阈值(字节)
     * 当单个文件大小超过此阈值时触发云查询
     */
    private long cloudSizeThreshold;
    
    /** 
     * 静默查询性能阈值
     * 控制静默查询时的系统资源使用限制
     */
    private int silentThreshold;
    
    /** 
     * 历史记录保存时长(毫秒)
     * 超过该时长的历史记录将被清理
     */
    private long historyTimeout;
    
    /** 
     * 历史记录最大条数
     * 超过该数量的历史记录将被清理
     */
    private long historySize;



    public Config(HashMap<String, Boolean> blackWhiteMap,
                  List<ScanFileType> scanTypes,
                  int cloudThreshold,
                  long cloudSizeThreshold,
                  int silentThreshold,
                  long historyTimeout,
                  long historySize) {
        this.blackWhiteMap = blackWhiteMap;
        this.scanTypes = scanTypes;
        this.cloudThreshold = cloudThreshold;
        this.cloudSizeThreshold = cloudSizeThreshold;
        this.silentThreshold = silentThreshold;
        this.historyTimeout = historyTimeout;
        this.historySize = historySize;
    }

    /**
     * 获取黑白名单列表
     * @return 返回当前的黑白名单HashMap，key为文件hash值，value为true表示白名单，false表示黑名单
     */
    private HashMap<String,Boolean> getBlackWhiteList(){
        return blackWhiteMap;
    }
    /**
     * 获取支持扫描的文件类型列表
     * @return 返回当前支持扫描的文件类型列表
     */
    private List<ScanFileType> getScanType() {
        return scanTypes;
    }

    /**
     * 获取云查询文件数量阈值
     * @return 返回当前的云查询文件数量阈值，当待扫描文件数量超过此值时触发云查询
     */
    private int getCloudCheckThreshold() {
        return cloudThreshold;
    }

    /**
     * 获取云查询文件大小阈值
     * @return 返回当前的云查询文件大小阈值(字节)，当单个文件大小超过此值时触发云查询
     */
    private long getCloudSizeThreshold() {
        return cloudSizeThreshold;
    }

    /**
     * 获取静默查询性能阈值
     * @return 返回当前的静默查询性能阈值，用于控制静默查询时的系统资源使用限制
     */
    private int getSilentPerformanceThreshold() {
        return silentThreshold;
    }

    /**
     * 获取历史记录保存时长
     * @return 返回当前的历史记录保存时长(毫秒)，超过该时长的历史记录将被清理
     */
    private long getHistoryTimeout() {
        return historyTimeout;
    }

    /**
     * 获取历史记录最大条数
     * @return 返回当前的历史记录最大条数，超过该数量的历史记录将被清理
     */
    private long getHistorySize(){
        return historySize;
    }

    /**
     * 创建并持久化当前配置
     * 将所有配置项保存到SharedPreferences和数据库中
     */
    public void create(){
        DataManager.getInstance().saveBlackWhiteList(blackWhiteMap);
        // 将 ScanFileType 枚举列表转换为字符串列表
        List<String> scanTypeStrings = ScanFileType.toStringList(scanTypes);
        DataManager.getInstance().saveScanType(scanTypeStrings);
        DataManager.getInstance().saveCloudCheckThreshold(cloudThreshold);
        DataManager.getInstance().saveCloudSizeThreshold(cloudSizeThreshold);
        DataManager.getInstance().saveSilentPerformanceThreshold(silentThreshold);
        DataManager.getInstance().saveHistoryTimeout(historyTimeout);
        DataManager.getInstance().saveHistorySize(historySize);
    }

    /**
     * 更新黑白名单列表
     * @param map 新的黑白名单列表，key为文件hash值，value为true表示白名单，false表示黑名单
     */
    public void updateBlackWhiteList(HashMap<String, Boolean> map) {
        if (map == null) return;
        this.blackWhiteMap = map;
        DataManager.getInstance().saveBlackWhiteList(map);
        // 更新缓存中对应的数据，避免缓存优先级高于黑白名单导致的冲突
        DataManager.getInstance().updateCacheForBlackWhiteList(map);
    }

    /**
     * 更新支持扫描的文件类型列表
     * @param types 新的文件类型列表，包含所有需要进行病毒扫描的文件类型
     */
    public void updateScanTypes(List<ScanFileType> types) {
        if (types == null) return;
        this.scanTypes = types;
        List<String> scanTypeStrings = ScanFileType.toStringList(scanTypes);
        DataManager.getInstance().saveScanType(scanTypeStrings);
    }

    /**
     * 更新云查询文件数量阈值
     * @param threshold 新的阈值，当待扫描文件数量超过此值时触发云查询，负值将被设置为默认值500
     */
    public void updateCloudThreshold(int threshold) {
        this.cloudThreshold = threshold < 0 ? 500 : threshold;
        DataManager.getInstance().saveCloudCheckThreshold(cloudThreshold);
    }

    /**
     * 更新云查询文件大小阈值
     * @param threshold 新的阈值(字节)，当单个文件大小超过此值时触发云查询，负值将被设置为默认值100MB
     */
    public void updateCloudSizeThreshold(long threshold) {
        this.cloudSizeThreshold = threshold < 0 ? (100 * 1024 * 1024) : threshold;
        DataManager.getInstance().saveCloudSizeThreshold(cloudSizeThreshold);
    }

    /**
     * 更新静默查询性能阈值
     * @param threshold 新的阈值，用于控制静默查询时的系统资源使用限制，负值将被设置为默认值100
     */
    public void updateSilentThreshold(int threshold) {
        this.silentThreshold = threshold < 0 ? 100 : threshold;
        DataManager.getInstance().saveSilentPerformanceThreshold(silentThreshold);
    }

    /**
     * 更新历史记录保存时长
     * @param timeout 新的时长(毫秒)，超过该时长的历史记录将被清理，负值将被设置为默认值7天
     */
    public void updateHistoryTimeout(long timeout) {
        this.historyTimeout = timeout < 0 ? (7 * 24 * 60 * 60 * 1000L) : timeout;
        DataManager.getInstance().saveHistoryTimeout(historyTimeout);
    }

    /**
     * 更新历史记录最大条数
     * @param size 新的记录数量上限，超过该数量的历史记录将被清理，负值将被设置为默认值1000
     */
    public void updateHistorySize(long size) {
        this.historySize = size < 0 ? 1000 : size;
        DataManager.getInstance().saveHistorySize(historySize);
    }

    public static class Builder {
        /**黑白名单表*/
        private HashMap<String,Boolean> blackWhiteMap;
        /**扫描类型*/
        private List<ScanFileType> scanTypes;
        /**云查阈值*/
        private int cloudThreshold = 500;
        /**云查文件大小阈值*/
        private long cloudSizeThreshold = 100 * 1024 * 1024;
        /**静默查阈值*/
        private int silentThreshold = 100;
        /**历史缓存时长*/
        private long historyTimeout = 7 * 24 * 60 * 60 * 1000L;
        /**历史缓存大小*/
        private long historySize = 1000;

        public Builder() {

        }

        /**
         * 设置黑白名单表
         *
         * @param map hash集合
         * @return Builder 返回Builder
         */
        public Builder setBlackWhiteList(HashMap<String, Boolean> map) {
            this.blackWhiteMap = map;
            return this;
        }

        /**
         * 设置扫描类型
         *
         * @param scanType 扫描类型
         * @return 返回Builder
         */
        public Builder setScanType(List<ScanFileType> scanType) {
            this.scanTypes = scanType;
            return this;
        }

        /**
         * 设置云查文件数量阈值
         *
         * @param threshold 阈值
         * @return 返回Builder
         */
        public Builder setCloudCheckThreshold(int threshold) {
            this.cloudThreshold = threshold < 0 ? 500 : threshold;
            return this;
        }

        /**
         * 设置云查文件大小阈值，当单个文件数据超过云查阈值大小则走云查
         * @param threshold
         * @return
         */
        public Builder setCloudSizeThreshold(long threshold) {
            this.cloudSizeThreshold = threshold < 0 ? (100 * 1024 * 1024) : threshold;
            return this;
        }

        /**
         * 设置静默查阈值
         *
         * @param threshold 阈值
         * @return 返回Builder
         */
        public Builder setSilentPerformanceThreshold(int threshold) {
            this.silentThreshold = threshold < 0 ? 100 : threshold;
            return this;
        }

        /**
         * 设置历史缓存时长 默认7天
         *
         * @param time 时长
         * @return 返回Builder
         */
        public Builder setHistoryTimeout(long time) {
            this.historyTimeout = time < 0 ? (7 * 24 * 60 * 60 * 1000L) : time;
            return this;
        }

        /**
         * 设置历史缓存的大小 默认1000
         *
         * @param size 大小
         * @return 返回Builder
         */
        public Builder setHistorySize(long size) {
            this.historySize = size < 0 ? 1000 : size;
            return this;
        }

        public Config build() {
            return new Config(blackWhiteMap,scanTypes,cloudThreshold,cloudSizeThreshold,silentThreshold,historyTimeout,historySize);
        }
    }
}
