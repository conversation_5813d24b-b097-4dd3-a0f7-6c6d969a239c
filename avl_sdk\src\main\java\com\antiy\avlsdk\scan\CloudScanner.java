package com.antiy.avlsdk.scan;

import android.text.TextUtils;

import com.antiy.avlsdk.AVLCoreEngine;
import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ResultCloudScan;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.storage.EncryptorHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 云扫描器类
 * 负责文件的云端扫描功能，支持批量扫描和本地扫描降级
 * 使用双线程池架构：
 * 1. hashExecutor: 负责计算文件哈希值
 * 2. scanExecutor: 负责执行实际的扫描任务
 */
public class CloudScanner extends BaseScanner {
    // 线程池相关常量
    private static final int HASH_CORE_POOL_SIZE = CORE_POOL_SIZE;
    private static final int HASH_MAX_POOL_SIZE = MAX_POOL_SIZE;
    private static final int SCAN_CORE_POOL_SIZE = 2;
    private static final int SCAN_MAX_POOL_SIZE = 4;
    // 用于计算文件哈希值的线程池
    private final ExecutorService hashExecutor;
    // 用于执行扫描任务的线程池
    private final ExecutorService scanExecutor;
    // 标记是否来自云端检查的标志位
    private boolean isFromCloudCheck;
    // 用于防止重复回调的标志位
    private final AtomicBoolean hasFinished = new AtomicBoolean(false);
    // 预计算的哈希值映射，避免重复计算
    private final Map<String, String> preCalculatedHashes = new ConcurrentHashMap<>();
    // 无效文件列表
    private List<File> invalidTypeFiles = new ArrayList<>();
    // 有效文件列表
    private List<File> validTypeFiles = new ArrayList<>();

    /**
     * 云扫描器构造函数
     * @param files 需要扫描的文件列表
     * @param callback 扫描回调接口
     * @param cloudCheck 是否为云端检查（如果为true，则在云端失败时会降级到本地扫描）
     */
    public CloudScanner(List<File> files, ScanListener callback, boolean cloudCheck) {
        this(files, callback, cloudCheck, null);
    }

    /**
     * 云扫描器构造函数（支持预计算哈希）
     * @param files 需要扫描的文件列表
     * @param callback 扫描回调接口
     * @param cloudCheck 是否为云端检查（如果为true，则在云端失败时会降级到本地扫描）
     * @param preCalculatedHashes 预计算的哈希值映射（文件路径 -> SHA256哈希值）
     */
    public CloudScanner(List<File> files, ScanListener callback, boolean cloudCheck, Map<String, String> preCalculatedHashes) {
        super(files, callback);
        // 初始化哈希计算线程池
        this.hashExecutor = new ThreadPoolExecutor(
                HASH_CORE_POOL_SIZE,
                HASH_MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>()
        );
        // 初始化扫描线程池，核心线程数2，最大线程数4
        this.scanExecutor = new ThreadPoolExecutor(
                SCAN_CORE_POOL_SIZE,
                SCAN_MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>()
        );
        this.isFromCloudCheck = cloudCheck;

        // 如果提供了预计算的哈希值，则存储起来避免重复计算
        if (preCalculatedHashes != null && !preCalculatedHashes.isEmpty()) {
            this.preCalculatedHashes.putAll(preCalculatedHashes);
            AVLEngine.Logger.info("CloudScanner initialized with " + preCalculatedHashes.size() + " pre-calculated hashes");
        }
    }

    /**
     * 开始扫描操作
     * 为每个文件创建哈希计算任务，并启动扫描处理线程
     */
    @Override
    public void startScan() {
        AVLEngine.Logger.info("CloudScanner startScan");
        // 检查引擎是否已初始化
        if (!AVLEngine.getInstance().getInitResult().isSuccess) {
            AVLEngine.Logger.error("Engine not initialized");
            return;
        }
        scanFileFilter();
        scanInvalidTypeFile();
        AVLEngine.Logger.info("validTypeFiles size: " + validTypeFiles.size());
        // 为每个文件创建哈希计算任务
        for (int i = 0; i < validTypeFiles.size(); i++) {
            final int index = i;
            File file = validTypeFiles.get(index);

            // 检查线程池状态，避免在已关闭的线程池中提交任务
            if (hashExecutor.isShutdown()) {
                AVLEngine.Logger.error("HashExecutor is shutdown, cannot submit task for file: " + file.getAbsolutePath());
                break;
            }

            try {
                hashExecutor.execute(() -> {
                    try {
                        // 再次检查停止状态
                        if (isStopped.get()) {
                            AVLEngine.Logger.info("Scan stopped, skipping task for file: " + file.getAbsolutePath());
                            return;
                        }
                        // 将任务添加到队列中等待处理
                        taskQueue.put(new ScanTask(index + invalidTypeFiles.size(), file));
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        AVLEngine.Logger.error("Task submission interrupted for file: " + file.getAbsolutePath());
                    } catch (Exception e) {
                        AVLEngine.Logger.error("Task submission failed for file: " + file.getAbsolutePath() +
                                             ", error: " + e.getClass().getSimpleName() + " - " + e.getMessage());
                    }
                });
            } catch (Exception e) {
                AVLEngine.Logger.error("Failed to submit hash task for file: " + file.getAbsolutePath() + ", error: " + e.getMessage());
                // 如果提交失败，继续处理下一个文件
            }
        }
        AVLEngine.Logger.info("taskQueue size:" + taskQueue.size());
        // 启动扫描处理线程
        if (!scanExecutor.isShutdown()) {
            scanExecutor.execute(this::processScan);
        } else {
            AVLEngine.Logger.error("ScanExecutor is shutdown, cannot start processScan");
        }
    }

    private void scanInvalidTypeFile() {
        AVLEngine.Logger.info("invalid file size:" + invalidTypeFiles.size());
        for (int i = 0; i < invalidTypeFiles.size(); i++) {
            File invalidFile = invalidTypeFiles.get(i);
            AVLEngine.Logger.info("invalid file:" + invalidFile.getName());
            callback.scanFileStart(i,invalidFile.getAbsolutePath());
            callback.scanFileFinish(i,invalidFile.getAbsolutePath(),new ResultScan("the file type is invalid"));
            currentIndex.incrementAndGet();
        }
    }

    private void scanFileFilter() {
        for (File file : files) {
            if (FileChecker.getInstance().isValidType(file)) {
                validTypeFiles.add(file);
            }else {
                invalidTypeFiles.add(file);
            }
        }
    }

    /**
     * 停止扫描操作
     * 立即关闭所有线程池并调用父类的停止方法
     */
    @Override
    public void stopScan() {
        super.stopScan();
        // 手动停止时使用强制关闭
        forceShutdownExecutors();
    }

    /**
     * 处理扫描任务的主循环
     * 从任务队列中批量获取任务并进行处理
     */
    private void processScan() {
        List<ScanTask> batch = new ArrayList<>();

        try {
            while (!isStopped.get() && (!taskQueue.isEmpty() || !batch.isEmpty())) {
                // 处理暂停请求
                handlePause();

                if (isStopped.get()) {
                    break;
                }

                if (isPaused.get()) continue;

                // 从队列中获取任务并添加到批次
                ScanTask task = taskQueue.poll();
                if (task != null) {
                    batch.add(task);
                }

                // 当批次达到上限或队列为空且批次不为空时，处理当前批次
                if (batch.size() >= BATCH_SIZE || (taskQueue.isEmpty() && !batch.isEmpty())) {
                    AVLEngine.Logger.info("processScan Remaining tasks size:" + taskQueue.size());
                    processCloudScanBatch(new ArrayList<>(batch));
                    batch.clear();
                }

                AVLEngine.Logger.info("processScan batch size:" + batch.size());

                // 检查是否所有任务都已完成
                if (taskQueue.isEmpty() && batch.isEmpty() && checkAllTasksFinished()) {
                    notifyScanFinish();
                    break;
                }
            }
        } catch (Exception e) {
            AVLEngine.Logger.error("processScan Cloud scan failed: " + e.getMessage());
        } finally {
            // 处理扫描停止的情况
            if (isStopped.get()) {
                callback.scanStop();
            }
            // 注意：移除线程池关闭逻辑，避免自我关闭问题
            // 线程池的关闭由stopScan()和notifyScanFinish()统一管理
        }
    }

    /**
     * 处理一批云扫描任务
     * @param batch 待处理的任务批次
     */
    private void processCloudScanBatch(List<ScanTask> batch) {
        AVLEngine.Logger.info("CloudScanner processCloudScanBatch");
        try {
            // 计算批次中所有文件的哈希值
            List<String> hashes = calculateBatchHashes(batch);
            // 使用云服务处理批次
            processBatchWithCloudService(batch, hashes);
        } catch (Exception e) {
            handleScanError(batch, e.getMessage());
        }
    }

    /**
     * 计算一批文件的哈希值
     * 优先使用预计算的哈希值，避免重复计算
     * @param batch 文件任务批次
     * @return 哈希值列表
     */
    private List<String> calculateBatchHashes(List<ScanTask> batch) {
        return batch.stream()
            .map(task -> {
                String filePath = task.file.getAbsolutePath();
                // 优先使用预计算的哈希值
                String preCalculatedHash = preCalculatedHashes.get(filePath);
                if (preCalculatedHash != null) {
                    AVLEngine.Logger.info("Using pre-calculated hash for: " + filePath);
                    return preCalculatedHash;
                } else {
                    // 如果没有预计算的哈希值，则现场计算
                    AVLEngine.Logger.info("Calculating hash for: " + filePath);
                    return EncryptorHelper.calcPathSHA256(filePath);
                }
            })
            .collect(Collectors.toList());
    }

    /**
     * 使用云服务处理批次任务
     * @param batch 任务批次
     * @param hashes 对应的哈希值列表
     */
    private void processBatchWithCloudService(List<ScanTask> batch, List<String> hashes) {
        AVLEngine.Logger.info("CloudScanner processBatchWithCloudService,hashes size:" + hashes.size() + "," + hashes );
        CloudScanService.getInstance().batchScanAsync(hashes, new CloudScanService.CloudScanCallback() {
            @Override
            public void onSuccess(List<ResultCloudScan> results) {
                processCloudScanResults(batch, results);
            }

            @Override
            public void onError(String error) {
                handleScanError(batch, error);
            }
        });
    }

    /**
     * 处理云扫描结果
     * @param batch 任务批次
     * @param results 云扫描结果列表
     */
    private void processCloudScanResults(List<ScanTask> batch, List<ResultCloudScan> results) {
        for (int i = 0; i < batch.size(); i++) {
            ScanTask task = batch.get(i);
            ResultCloudScan cloudScan = results.get(i);
            processIndividualScanResult(task, cloudScan);
        }
        // 在这里检查是否所有任务都已完成
        if (checkAllTasksFinished() && taskQueue.isEmpty()) {
            notifyScanFinish();
        }
    }

    /**
     * 处理单个文件的扫描结果
     * @param task 扫描任务
     * @param cloudScan 云扫描结果
     */
    private void processIndividualScanResult(ScanTask task, ResultCloudScan cloudScan) {
        // 通知开始扫描文件
        callback.scanFileStart(task.index, task.file.getAbsolutePath());
        // 创建扫描结果
        ResultScan resultScan = createScanResult(cloudScan);
        // 通知扫描完成
        callback.scanFileFinish(task.index, task.file.getAbsolutePath(), resultScan);
        // 缓存扫描结果
        CacheManager.storeScanResult(task.file.getAbsolutePath(), cloudScan.virusName, cloudScan.sha256);
        // 更新进度
        if (incrementAndCheckFinish()) {
            notifyScanFinish();
        }
    }

    /**
     * 根据云扫描结果创建最终的扫描结果对象
     * @param cloudScan 云扫描结果
     * @return 标准扫描结果对象
     */
    private ResultScan createScanResult(ResultCloudScan cloudScan) {
        return new ResultScan(
                !TextUtils.isEmpty(cloudScan.virusName),
                cloudScan.virusName,
                cloudScan.sha256,
                true
        );
    }

    /**
     * 处理扫描错误
     * 如果是来自云端检查，则降级到本地扫描；否则直接处理错误
     * @param batch 发生错误的任务批次
     * @param errorMessage 错误信息
     */
    private void handleScanError(List<ScanTask> batch, String errorMessage) {
        AVLEngine.Logger.error("Cloud scan error: " + errorMessage + ", isFromCloudCheck=" + isFromCloudCheck);
        if (isFromCloudCheck) {
            // 降级到本地扫描
            processLocalScanBatch(batch);
        } else {
            // 直接处理错误
            processBatchError(batch);
        }
    }

    /**
     * 处理批次错误
     * 为批次中的每个任务创建一个失败的扫描结果
     * @param batch 任务批次
     */
    private void processBatchError(List<ScanTask> batch) {
        for (ScanTask task : batch) {
            ResultScan resultScan = new ResultScan(false);
            callback.scanFileFinish(task.index, task.file.getAbsolutePath(), resultScan);
            if (incrementAndCheckFinish()) {
                notifyScanFinish();
            }
        }
    }

    /**
     * 增加计数并检查是否所有任务都已完成
     * @return 是否所有任务都已完成
     */
    private boolean incrementAndCheckFinish() {
        return currentIndex.incrementAndGet() >= files.size();
    }

    /**
     * 检查是否所有任务都已完成
     * @return 是否所有任务都已完成
     */
    private boolean checkAllTasksFinished() {
        AVLEngine.Logger.info("CloudScanner checkAllTasksFinished,currentIndex=" + currentIndex.get() + ",files.size=" + files.size());
        return currentIndex.get() >= files.size();
    }

    /**
     * 通知扫描完成
     * 使用CAS操作确保只回调一次，并负责资源清理
     */
    private void notifyScanFinish() {
        AVLEngine.Logger.info("CloudScanner notifyScanFinish,hasFinished=" + hasFinished);
        if (hasFinished.compareAndSet(false, true)) {
            AVLEngine.Logger.info("CloudScanner notifyScanFinish");
            try {
                callback.scanFinish();
            } finally {
                // 扫描正常完成时关闭线程池
                shutdownExecutors();
            }
        }
    }

    /**
     * 安全关闭线程池
     * 使用优雅关闭方式，避免强制中断正在执行的任务
     */
    private void shutdownExecutors() {
        AVLEngine.Logger.info("CloudScanner shutdownExecutors");
        try {
            // 优雅关闭hashExecutor
            if (!hashExecutor.isShutdown()) {
                hashExecutor.shutdown();
                AVLEngine.Logger.info("HashExecutor shutdown initiated");
            }

            // 优雅关闭scanExecutor
            if (!scanExecutor.isShutdown()) {
                scanExecutor.shutdown();
                AVLEngine.Logger.info("ScanExecutor shutdown initiated");
            }
        } catch (Exception e) {
            AVLEngine.Logger.error("Error during executor shutdown: " + e.getMessage());
            // 如果优雅关闭失败，强制关闭
            forceShutdownExecutors();
        }
    }

    /**
     * 强制关闭线程池
     * 用于异常情况或需要立即停止的场景
     */
    private void forceShutdownExecutors() {
        AVLEngine.Logger.info("CloudScanner forceShutdownExecutors");
        try {
            if (!hashExecutor.isShutdown()) {
                hashExecutor.shutdownNow();
                AVLEngine.Logger.info("HashExecutor force shutdown");
            }

            if (!scanExecutor.isShutdown()) {
                scanExecutor.shutdownNow();
                AVLEngine.Logger.info("ScanExecutor force shutdown");
            }
        } catch (Exception e) {
            AVLEngine.Logger.error("Error during force shutdown: " + e.getMessage());
        }
    }

    /**
     * 使用本地扫描处理批次任务
     * 当云扫描失败时的降级处理方案
     * @param currentBatch 当前任务批次
     */
    private void processLocalScanBatch(List<ScanTask> currentBatch) {
        AVLEngine.Logger.error("processLocalScanBatch currentBatch:" + currentBatch);
        for (int i = 0; i < currentBatch.size(); i++) {
            ScanTask scanTask = currentBatch.get(i);
            // 通知开始扫描文件
            callback.scanFileStart(scanTask.index, scanTask.file.getAbsolutePath());
            // 执行本地扫描
            ResultScan result = FileScanner.scan(scanTask.file.getAbsolutePath(),true);
            // 通知扫描完成
            callback.scanFileFinish(scanTask.index, scanTask.file.getAbsolutePath(), result);
            // 更新进度
            currentIndex.incrementAndGet();
            AVLEngine.Logger.info("processLocalScanBatch currentIndex:" + currentIndex.get());
        }
        if(checkAllTasksFinished()){
            notifyScanFinish();
        }
    }
}
