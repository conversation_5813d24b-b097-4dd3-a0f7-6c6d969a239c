## 1.简介
AVL SDK提供了一系列功能如扫描、更新、暂停扫描、停止扫描就、鉴权认证等。旨在帮助开发者快速集成和使用我们的服务。本文档将指导您如何在Android项目中成功接入和使用AVL SDK。
## 2.环境要求
在开始集成之前，请确保您的开发环境满足以下要求
* Android Studio 版本：4.0 及以上
* Android SDK 版本：API 21 及以上
* Gradle 版本：6.0 及以上
## 3.权限配置
根据SDK的功能需求，在AndroidManifest.xml文件中添加必要的权限.
```java
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.INTERNET"  />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```
**注意**：请确保在您的应用中正确处理动态权限申请，特别是针对Android 6.0及以上版本。
## 4.接入步骤

### **4.1. 添加AAR 文件**

* 将下载的AAR文件放入项目的libs目录中。
* 如果libs目录不存在，请在项目根目录下手动创建。

### **4.2. 配置Gradle**

在应用模块的build.gradle文件中，添加以下依赖
```java
dependencies {
    implementation(name: 'sdk-name', ext: 'aar')
}
```
**4.3. 初始化SDK**

在您的Application类或主Activity中初始化 SDK。以下是一个示例：
```
import com.antiy.avlsdk.AVLEngine;
 
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        // 初始化 SDK，示例，注意：这里需要再获取读写权限后进行init，这里这么写只是为了方便查看
        AVLEngine.init(this, uuid, ALog(this), NetworkManager())
    }
}
```
**注意**：

* uuid是车辆的唯一标识符。
* ALog是com.antiy.avlsdk.callback.Logger接口的实现。
* NetworkManager是com.antiy.avlsdk.callback.NetworkTunnel接口的实现。
* 这些实现类需要由接入方自行提供。
* init之前需要确保程序已经获取了读取权限
Logger.java
```java
public interface Logger {
    /**
     * 最冗余的信息
     * @param msg
     */
    void verbose(String msg);
 
    /**
     * 提示信息
     * @param msg
     */
    void info(String msg);
 
    /**
     * 比提示信息更重要，调试相关的
     * @param msg
     */
    void debug(String msg);
 
    /**
     * 警告信息，非致命错误
     * @param msg
     */
    void warn(String msg);
 
    /**
     * 错误信息，接口内部报错，不影响sdk
     * @param msg
     */
    void error(String msg);
 
    /**
     * 致命错误，导致sdk、app崩溃的信息
     * @param msg
     */
    void fatal(String msg);
}
```
NetworkTunnel.java
```java
public interface NetworkTunnel {
    /**
     * 网络是否可用
     * @return
     */
    boolean isAvailable();
 
    /**
     * 请求转发方法，通过RequestCallback回调返回给调用方
     * @param uri 目标uri，不包含scheme://hostname部分
     * @param method 枚举类，可用的http方法
     * @param param 请求参数
     * @param callback 回调对象
     */
    void request(
            String uri,
            RequestMethod method,
            Object param,
            RequestCallback callback);
 
 
    /**
     * 文件下载转发方法，下载完成通过DownloadCallback回调返回给调用方
     * @param uri 目标下载路径，不包含scheme://hostname部分
     * @param callback 回调对象
     */
    void download(String uri, DownloadCallback callback);
    
    /**
     * 取消请求的方法
     */
    default void cancelRequest() {}
}
```
### 5.使用 SDK 功能

在使用扫描功能之前，必须先配置SDK相关的黑白名单、扫描类型、云查阈值、缓存时长、缓存大小等参数，配置完成之后调用scan函数进行扫描.
```java
import com.antiy.avlsdk.AVLEngine;
 
public class MainActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
 
        // 配置 SDK 功能
       AVLEngine.getInstance().loadConfig(
            Config.Builder()
                .setBlackWhiteList(whiteMap)
                .setScanType(scanList)
                .setHistorySize(1024)
                .setHistoryTimeout(60 * 60 * 24 * 10)
                .setCloudCheckThreshold(10000)
                .setCloudSizeThreshold(100 * 1024 * 1024)
                .setSilentPerformanceThreshold(10000)
                .build()
        //扫描 耗时任务子线程扫描
        thread {
            AVLEngine.getInstance().scanDir(dir)
        }
    
    }
}
```
### 6.自定义实现

为了正确使用SDK，您需要实现两个关键接口：Logger和NetworkTunnel

以下是示例实现，仅供参考：

#### 6.1 ALogger.java
```java
import android.content.Context
import android.util.Log
import com.antiy.avlsdk.callback.Logger
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.Date
 
class ALog : Logger{
 
    companion object {
        private const val LOG_TAG = "AVL_SDK"
        private const val LOG_TO_CONSOLE = true // 是否打印到控制台
        private const val LOG_TO_FILE = true // 是否写入文件
        private const val LOG_DIR = "app_logs" // 日志文件夹名称
        private const val LOG_FILE_NAME = "app_log.txt" // 日志文件名
    }
 
    private var logFile: File? = null
 
    constructor(context : Context) {
        initLogFile(context)
    }
 
    /**
     * 初始化日志文件
     * @param context 应用上下文
     */
    private fun initLogFile(context: Context){
        if (LOG_TO_FILE) {
            val dir = File(context.filesDir, LOG_DIR)
            if (!dir.exists()) {
                dir.mkdirs()
            }
            logFile = File(dir, LOG_FILE_NAME)
        }
    }
 
    /**
     * 打印日志并写入文件
     * @param tag 标签
     * @param message 消息
     * @param level 日志级别
     */
    private fun log(tag: String, message: String, level: Int) {
        if (LOG_TO_CONSOLE) {
            when (level) {
                Log.VERBOSE -> Log.v(tag, message)
                Log.DEBUG -> Log.d(tag, message)
                Log.INFO -> Log.i(tag, message)
                Log.WARN -> Log.w(tag, message)
                Log.ERROR -> Log.e(tag, message)
                Log.ASSERT -> Log.wtf(tag, message)
            }
        }
 
        if (LOG_TO_FILE && logFile != null) {
            writeToFile(tag, message, level)
        }
    }
 
    /**
     * 将日志写入文件
     * @param tag 标签
     * @param message 消息
     * @param level 日志级别
     */
    private fun writeToFile(tag: String, message: String, level: Int) {
        BufferedWriter(FileWriter(logFile, true)).use { writer ->
            val logMessage = getLogMessage(tag, message, level)
            writer.write(logMessage)
            writer.newLine()
        }
    }
 
    /**
     * 获取格式化的日志消息
     * @param tag 标签
     * @param message 消息
     * @param level 日志级别
     * @return 格式化的日志消息
     */
    private fun getLogMessage(tag: String, message: String, level: Int): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        val date = dateFormat.format(Date())
        val levelString = getLevelString(level)
        return "$date [$levelString] $tag: $message"
    }
 
    /**
     * 获取日志级别的字符串表示
     * @param level 日志级别
     * @return 日志级别的字符串
     */
    private fun getLevelString(level: Int): String {
        return when (level) {
            Log.VERBOSE -> "V"
            Log.DEBUG -> "D"
            Log.INFO -> "I"
            Log.WARN -> "W"
            Log.ERROR -> "E"
            Log.ASSERT -> "A"
            else -> "U"
        }
    }
 
    // 公共方法
    override fun verbose(msg: String?) {
       log(LOG_TAG,msg!!,Log.VERBOSE)
    }
 
    override fun info(msg: String?) {
        log(LOG_TAG,msg!!,Log.INFO)
    }
 
    override fun debug(msg: String?) {
        log(LOG_TAG,msg!!,Log.DEBUG)
    }
 
    override fun warn(msg: String?) {
        log(LOG_TAG,msg!!,Log.WARN)
    }
 
    override fun error(msg: String?) {
        log(LOG_TAG,msg!!,Log.ERROR)
    }
 
    override fun fatal(msg: String?) {
        log(LOG_TAG,msg!!,Log.ERROR)
    }
}
```
#### 6.2NetworkManager.java

```java
import android.content.Context
import android.net.ConnectivityManager
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.callback.DownloadCallback
import com.antiy.avlsdk.callback.NetworkTunnel
import com.antiy.avlsdk.callback.RequestCallback
import com.antiy.avlsdk.entity.RequestMethod
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import java.io.File
import java.io.IOException


/**
 * Author: mr.wang
 * Date: 2024/9/26 16:39
 * Description:
 */
open class NetworkManager : NetworkTunnel {
    private val DOWNLOAD_BARS_URL: String = "https://the-xiaomi.oss-cn-beijing.aliyuncs.com/";
    private val BASE_URL: String = "http://************:8080"
    companion object {
        const val CONTENT_TYPE = "application/json;charset=UTF-8"
    }
    private val okHttpClient by lazy {
        OkHttpClient.Builder()
            .addInterceptor(HttpLoggingInterceptor(CustomLogger()).apply {
                this.level = HttpLoggingInterceptor.Level.BODY
            })
            .build()
    }
    private var currentCall: Call? = null
    private var downloadCall: Call? = null

    override fun isAvailable(): Boolean {
        val connectivityManager = AVLEngine.getInstance().context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetworkInfo = connectivityManager.activeNetworkInfo
        return activeNetworkInfo != null && activeNetworkInfo.isConnected
    }

    override fun request(
        uri: String?,
        method: RequestMethod?,
        param: Any?,
        callback: RequestCallback?
    ) {
        val contentType = CONTENT_TYPE.toMediaTypeOrNull()
        val body = RequestBody.create(contentType, param.toString())
        val request = Request.Builder()
            .url(BASE_URL + uri!!)
            .post(body)
            .build()
        currentCall = okHttpClient.newCall(request)
        currentCall?.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                if (!call.isCanceled()) {
                    callback?.onError(e.message)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                if (!call.isCanceled()) {
                    response.use {
                        callback?.onFinish(response.code, response.body?.string())
                    }
                }
            }
        })
    }

    override fun download(uri: String?, callback: DownloadCallback?) {
        val request = Request.Builder().url(DOWNLOAD_BARS_URL + uri!!).build()
        downloadCall = okHttpClient.newCall(request)
        downloadCall?.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                if (!call.isCanceled()) {
                    callback?.onError(e.message)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                if (!call.isCanceled()) {
                    DownloadManager.download(uri, response, callback)
                }
            }
        })
    }

    override fun cancelRequest() {
        currentCall?.cancel()
        downloadCall?.cancel()
        currentCall = null
        downloadCall = null
    }
}

object DownloadManager {
    /**
     * @param uri 下载地址
     * @param response 响应
     * @param callback 下载回调
     */
    fun download(uri : String,response: Response,callback: DownloadCallback?){
        AVLEngine.Logger.info("download url : $uri")
        response.body?.byteStream()?.use { inputStream ->
            // 假设保存到本地的逻辑
            val fileName = uri.substring(uri.lastIndexOf("/") + 1)
            val file = File("/sdcard/Download/$fileName")
            file.outputStream().use { outputStream ->
                inputStream.copyTo(outputStream)
            }
            callback?.onFinish(200, fileName,file.absolutePath)
        }
    }
}
```
### **7. 注意事项**

* init方法初始化之前必须先要获取读写权限
* 确保正确处理所有必要的权限，特别是在Android 6.0及以上版本中的动态权限申请。
* 在使用SDK功能之前，必须先调用loadConfig方法进行配置。
* 网络操作应在后台线程中执行，避免阻塞主线程。
* 日志实现应考虑性能影响，避免在生产环境中过度日志记录。