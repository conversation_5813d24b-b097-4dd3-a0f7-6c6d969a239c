{"buildFiles": ["D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\src\\main\\cpp\\argparse\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"cpu_usage-Debug-arm64-v8a": {"artifactName": "cpu_usage", "buildCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a", "cpu_usage"], "abi": "arm64-v8a", "output": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj\\arm64-v8a\\cpu_usage", "runtimeFiles": []}, "argparse_shared-Debug-arm64-v8a": {"artifactName": "argparse_shared", "buildCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a", "argparse_shared"], "abi": "arm64-v8a", "output": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj\\arm64-v8a\\libargparse.so", "runtimeFiles": []}, "argparse_static-Debug-arm64-v8a": {"artifactName": "argparse_static", "buildCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a", "argparse_static"], "abi": "arm64-v8a", "output": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a\\src\\main\\cpp\\argparse\\libargparse_static.a", "runtimeFiles": []}}}