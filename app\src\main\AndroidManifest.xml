<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET"  />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AntiVirus"
        android:usesCleartextTraffic="true"
        tools:targetApi="31"
        tools:replace="android:name">
        <activity
            android:name=".activity.HomeActivity"
            android:exported="true"
            tools:replace="android:name">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="content" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="*/*" />
            </intent-filter>
        </activity>
        <activity 
            android:name=".activity.UuidActivity"
            tools:replace="android:name" />
        <activity
            android:name=".activity.ScanningActivity"
            android:exported="false"
            tools:replace="android:name" />
        <activity
            android:name=".activity.AntivirusSettingsActivity"
            android:exported="false"
            tools:replace="android:name" />
        <activity
            android:name=".activity.CloudScanSettingsActivity"
            android:exported="false"
            tools:replace="android:name" />
        <activity
            android:name=".activity.PerformanceSettingsActivity"
            android:exported="false"
            tools:replace="android:name" />
        <activity
            android:name=".activity.HelpFeedbackActivity"
            android:exported="false"
            tools:replace="android:name" />
        <activity
            android:name=".activity.MainActivity"
            android:exported="false"
            tools:replace="android:name" />
        <activity
            android:name=".activity.ScanResultActivity"
            android:exported="false"
            tools:replace="android:name" />
        <activity
            android:name=".activity.AboutActivity"
            android:exported="false"
            android:theme="@style/Theme.About"
            tools:replace="android:name" />
    </application>

</manifest>