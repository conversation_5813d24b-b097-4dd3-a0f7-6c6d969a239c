# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 147ms
create_cxx_tasks completed in 149ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 137ms
create_cxx_tasks completed in 138ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 156ms
create_cxx_tasks completed in 158ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 20ms
    create-X86-model 12ms
    create-X86_64-model 16ms
    create-module-model
      create-cmake-model 31ms
      [gap of 32ms]
    create-module-model completed in 63ms
    create-ARMEABI_V7A-model 70ms
    create-X86-model 16ms
    create-module-model 18ms
    create-ARMEABI_V7A-model 11ms
    create-X86_64-model 16ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 52ms
    create-X86-model 27ms
    create-X86_64-model 16ms
    create-module-model
      create-cmake-model 16ms
    create-module-model completed in 16ms
    [gap of 10ms]
    create-variant-model 38ms
    create-X86-model 48ms
    create-module-model
      create-ndk-meta-abi-list 11ms
    create-module-model completed in 11ms
    create-ARMEABI_V7A-model 31ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 574ms
  [gap of 16ms]
create_cxx_tasks completed in 590ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 123ms
create_cxx_tasks completed in 125ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 17ms
    [gap of 117ms]
    create-variant-model 11ms
    [gap of 19ms]
  create-initial-cxx-model completed in 184ms
create_cxx_tasks completed in 185ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 28ms]
    create-module-model 10ms
    [gap of 163ms]
  create-initial-cxx-model completed in 212ms
create_cxx_tasks completed in 214ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 141ms
create_cxx_tasks completed in 143ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 134ms
create_cxx_tasks completed in 136ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 119ms
create_cxx_tasks completed in 121ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 133ms
create_cxx_tasks completed in 135ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 143ms
create_cxx_tasks completed in 145ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 137ms
create_cxx_tasks completed in 142ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 137ms]
    create-variant-model 12ms
    [gap of 21ms]
  create-initial-cxx-model completed in 170ms
create_cxx_tasks completed in 174ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 176ms]
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 192ms
create_cxx_tasks completed in 194ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 161ms
create_cxx_tasks completed in 162ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 112ms
create_cxx_tasks completed in 115ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 151ms
create_cxx_tasks completed in 153ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 125ms
create_cxx_tasks completed in 127ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    [gap of 26ms]
    create-module-model 10ms
    [gap of 78ms]
  create-initial-cxx-model completed in 129ms
create_cxx_tasks completed in 130ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 190ms
create_cxx_tasks completed in 194ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 131ms
create_cxx_tasks completed in 133ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 66ms
create_cxx_tasks completed in 67ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 73ms
create_cxx_tasks completed in 75ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 31ms]
    create-module-model 10ms
    [gap of 113ms]
  create-initial-cxx-model completed in 154ms
create_cxx_tasks completed in 158ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 143ms
create_cxx_tasks completed in 146ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 80ms
create_cxx_tasks completed in 81ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 152ms
create_cxx_tasks completed in 156ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 168ms]
    create-module-model 10ms
    [gap of 28ms]
  create-initial-cxx-model completed in 217ms
create_cxx_tasks completed in 219ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 147ms
create_cxx_tasks completed in 148ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 155ms
create_cxx_tasks completed in 157ms

