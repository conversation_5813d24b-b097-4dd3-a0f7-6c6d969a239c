package com.antiy.avlsdk.storage;

import static com.antiy.avlsdk.storage.DBConstants.SHARE_PREFERENCES_CONFIG;

import android.content.Context;
import android.content.SharedPreferences;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.config.ConfigKey;
import com.antiy.avlsdk.entity.BlackWhiteEntity;
import com.antiy.avlsdk.entity.CacheEntity;
import com.antiy.avlsdk.utils.JsonUtils;

import java.util.HashMap;
import java.util.List;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: StorageManager
 * 4 * Author: wangbiao
 * 5 * Date: 2024/9/3 16:26
 * 6 * Description:负责存储和读取数据的主要接口类对外提供
 * 10
 */
public class DataManager {
    private DatabaseHelper dbHelper;
    private EncryptorHelper encryptor;
    private static DataManager mInstance;
    private SharedPreferences preferences;

    public static DataManager getInstance() {
        if (mInstance == null) {
            synchronized (DataManager.class) {
                if (mInstance == null) {
                    mInstance = new DataManager(AVLEngine.getInstance().getContext());
                }
            }

        }
        return mInstance;
    }

    private DataManager(Context context) {
        this.dbHelper = new DatabaseHelper(context);
        this.encryptor = new EncryptorHelper();
        preferences = AVLEngine.getInstance().getContext().getSharedPreferences(SHARE_PREFERENCES_CONFIG, Context.MODE_PRIVATE);
    }

    public int getCloudCheckThreshold() {
        int value = preferences.getInt(ConfigKey.CLOUDSCAN_COUNT.toString(), 500);
        return value < 0 ? 500 : value;
    }

    /**
     * 获取云查文件阈值大小
     * @return
     */
    public long getCloudSizeThreshold() {
        long value = preferences.getLong(ConfigKey.CLOUDSCAN_SIZE.toString(),100 * 1024 * 1024);
        return value < 0 ? (100 * 1024 * 1024) : value;
    }

    public int getSilentPerformanceThreshold() {
        int value = preferences.getInt(ConfigKey.SILENTSCAN_RES.toString(), 100);
        return value < 0 ? 100 : value;
    }

    /**
     * 获取历史缓存的阈值
     * @return
     */
    public long getHistoryCacheSize() {
        long value = preferences.getLong(ConfigKey.HISTORY_SIZE.toString(),1000);
        return value < 0 ? 1000 : value;
    }

    /**
     * 历史缓存时长，
     * @return
     */
    public long getHistoryCacheTimeout() {
        long value = preferences.getLong(ConfigKey.HISTORY_TIMEOUT.toString(),7 * 24 * 60 * 60 * 1000);
        return value < 0 ? (7 * 24 * 60 * 60 * 1000) : value;
    }

    /**
     * 清除黑白名单数据
     */
    public void clearBlackWhiteList() {
        dbHelper.clearAllData(DBConstants.TABLE_BLACK_WHITE);
    }

    /**
     * 删除单条黑白名单记录
     * @param hash 要删除的文件hash值
     */
    public void deleteBlackWhiteItem(String hash) {
        if (hash == null || hash.trim().isEmpty()) {
            return;
        }
        dbHelper.deleteData(DBConstants.TABLE_BLACK_WHITE, DBConstants.BLACK_WHITE_COLUMN_HASH, hash);
    }

    /**
     * 清除所有缓存数据
     */
    public void clearCacheData() {
        dbHelper.clearAllData(DBConstants.TABLE_CACHE);
    }

    /**
     * 存储黑白名单表
     */
    public void saveBlackWhiteList(HashMap<String, Boolean> map) {
        if (map == null) {
            return;
        }
        for (String key : map.keySet()) {
            dbHelper.insertBlackWhiteData(key, map.get(key).toString());
        }
    }

    /**
     * 根据黑白名单更新缓存中对应的数据
     * 当黑白名单更新时，需要同步更新缓存中相同hash的virusName，避免缓存优先级导致的冲突
     * @param map 黑白名单映射，key为hash值，value为true表示白名单，false表示黑名单
     */
    public void updateCacheForBlackWhiteList(HashMap<String, Boolean> map) {
        if (map == null) {
            return;
        }

        for (String hash : map.keySet()) {
            // 检查缓存中是否存在该hash的数据
            CacheEntity existingCache = getCacheData(hash);
            if (existingCache != null) {
                // 根据黑白名单设置更新virusName
                Boolean isWhite = map.get(hash);
                String newVirusName;
                if (isWhite != null && isWhite) {
                    // 白名单：设置为空字符串表示安全
                    newVirusName = "";
                } else {
                    // 黑名单：设置为黑名单标识
                    newVirusName = "黑名单病毒";
                }

                // 更新缓存数据，保持原有的时间戳
                saveCacheData(hash, newVirusName, existingCache.getTimestamp());
                AVLEngine.Logger.info("Updated cache for hash: " + hash + ", isWhite: " + isWhite + ", virusName: " + newVirusName);
            }
        }
    }

    /**
     * 获取黑白名单列表
     */
    public HashMap<String, Boolean> getBlackWhiteListData() {
        return dbHelper.getBlackWhiteListData();
    }

    /**
     * 根据hash值获取实体
     * @param hash
     * @return
     */
    public BlackWhiteEntity getBlackWhiteData(String hash) {
        return dbHelper.matchBlackWhiteData(hash);
    }

    /**
     * 保持缓存数据表
     *
     * @param hash
     * @param virusName
     * @param timestamp
     */
    public void saveCacheData(String hash, String virusName, long timestamp) {
        dbHelper.insertCacheData(hash, virusName, timestamp);
    }

    public CacheEntity getCacheData(String key) {
        return dbHelper.getCacheData(key);
    }

    public List<CacheEntity> getAllCacheData() {
        List<CacheEntity> caches = dbHelper.getAllCacheData();
        return caches;
    }


    public void deleteData(String tableName, String columnName, String key) {
        dbHelper.deleteData(tableName, columnName, key);
    }

    /**
     * 根据表名删除所有数据
     *
     * @param tableName
     */
    public void clearAllData(String tableName) {
        dbHelper.clearAllData(tableName);
    }

    /**
     * 清理数据库无效的缓存数据
     */
    public void maintainDatabase() {
        dbHelper.maintainDatabase();
    }

    public void saveCloudCheckThreshold(int threshold) {
        int validThreshold = threshold < 0 ? 500 : threshold;
        preferences.edit().putInt(ConfigKey.CLOUDSCAN_COUNT.toString(), validThreshold).apply();
    }

    public void saveCloudSizeThreshold(long threshold) {
        long validThreshold = threshold < 0 ? (100 * 1024 * 1024) : threshold;
        preferences.edit().putLong(ConfigKey.CLOUDSCAN_SIZE.toString(), validThreshold).apply();
    }

    public void saveSilentPerformanceThreshold(int threshold) {
        int validThreshold = threshold < 0 ? 100 : threshold;
        preferences.edit().putInt(ConfigKey.SILENTSCAN_RES.toString(), validThreshold).apply();
    }

    public void saveHistoryTimeout(long timeout) {
        long validTimeout = timeout < 0 ? (7 * 24 * 60 * 60 * 1000L) : timeout;
        preferences.edit().putLong(ConfigKey.HISTORY_TIMEOUT.toString(), validTimeout).apply();
    }

    public void saveHistorySize(long size) {
        long validSize = size < 0 ? 1000 : size;
        preferences.edit().putLong(ConfigKey.HISTORY_SIZE.toString(), validSize).apply();
    }

    public void saveScanType(List<String> types) {
        if (types == null) return;
        if (!types.isEmpty()) {
            preferences.edit().putString(ConfigKey.SCAN_TYPE.toString(), JsonUtils.listToJson(types)).apply();
        }
    }
}
