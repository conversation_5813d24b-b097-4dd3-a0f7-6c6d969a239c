<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Android\cccj-sdk\app\src\main\res"/><source path="D:\Projects\Android\cccj-sdk\app\build\generated\res\rs\greatwall\debug"/><source path="D:\Projects\Android\cccj-sdk\app\build\generated\res\resValues\greatwall\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Android\cccj-sdk\app\src\main\res"><file name="bottom_nav_colors" path="D:\Projects\Android\cccj-sdk\app\src\main\res\color\bottom_nav_colors.xml" qualifiers="" type="color"/><file name="bg_app_logo" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\bg_app_logo.xml" qualifiers="" type="drawable"/><file name="bg_circle_blue" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\bg_circle_blue.xml" qualifiers="" type="drawable"/><file name="bg_edittext_rounded" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\bg_edittext_rounded.xml" qualifiers="" type="drawable"/><file name="button_primary" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="circle_background_blue" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\circle_background_blue.xml" qualifiers="" type="drawable"/><file name="circle_background_light_blue" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\circle_background_light_blue.xml" qualifiers="" type="drawable"/><file name="circle_background_light_red" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\circle_background_light_red.xml" qualifiers="" type="drawable"/><file name="gray_rounded_button_background" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\gray_rounded_button_background.xml" qualifiers="" type="drawable"/><file name="ic_antivirus_engine" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_antivirus_engine.xml" qualifiers="" type="drawable"/><file name="ic_app" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_app.xml" qualifiers="" type="drawable"/><file name="ic_app_manage" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_app_manage.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_back" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_check" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_cleanup" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_cleanup.xml" qualifiers="" type="drawable"/><file name="ic_cloud" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_cloud.xml" qualifiers="" type="drawable"/><file name="ic_custom_scan" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_custom_scan.xml" qualifiers="" type="drawable"/><file name="ic_dark_mode" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_dark_mode.xml" qualifiers="" type="drawable"/><file name="ic_deep_scan" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_deep_scan.xml" qualifiers="" type="drawable"/><file name="ic_email" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_email.xml" qualifiers="" type="drawable"/><file name="ic_file" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_file.xml" qualifiers="" type="drawable"/><file name="ic_full_scan" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_full_scan.xml" qualifiers="" type="drawable"/><file name="ic_help" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_help.xml" qualifiers="" type="drawable"/><file name="ic_home" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_info" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_language" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_language.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_more" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_more.xml" qualifiers="" type="drawable"/><file name="ic_network" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_network.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_performance" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_performance.xml" qualifiers="" type="drawable"/><file name="ic_performance_settings" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_performance_settings.xml" qualifiers="" type="drawable"/><file name="ic_person" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_play" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_privacy" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_privacy.xml" qualifiers="" type="drawable"/><file name="ic_protection" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_protection.xml" qualifiers="" type="drawable"/><file name="ic_quick_scan" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_quick_scan.xml" qualifiers="" type="drawable"/><file name="ic_realtime_protection" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_realtime_protection.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_scan" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_scan.xml" qualifiers="" type="drawable"/><file name="ic_scan_strategy" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_scan_strategy.xml" qualifiers="" type="drawable"/><file name="ic_schedule" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_schedule.xml" qualifiers="" type="drawable"/><file name="ic_scheduled_scan" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_scheduled_scan.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_shield" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_shield.xml" qualifiers="" type="drawable"/><file name="ic_shield_logo" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_shield_logo.xml" qualifiers="" type="drawable"/><file name="ic_support" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_support.xml" qualifiers="" type="drawable"/><file name="ic_virus_alert" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_virus_alert.xml" qualifiers="" type="drawable"/><file name="ic_virus_db" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_virus_db.xml" qualifiers="" type="drawable"/><file name="ic_virus_scan" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_virus_scan.xml" qualifiers="" type="drawable"/><file name="ic_warning" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_warning.xml" qualifiers="" type="drawable"/><file name="ic_whitelist" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_whitelist.xml" qualifiers="" type="drawable"/><file name="rounded_button_background" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\rounded_button_background.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="activity_about" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_about.xml" qualifiers="" type="layout"/><file name="activity_antivirus_settings" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_antivirus_settings.xml" qualifiers="" type="layout"/><file name="activity_cloud_scan_settings" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_cloud_scan_settings.xml" qualifiers="" type="layout"/><file name="activity_help_feedback" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_help_feedback.xml" qualifiers="" type="layout"/><file name="activity_home" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_home.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_performance_settings" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_performance_settings.xml" qualifiers="" type="layout"/><file name="activity_scanning" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_scanning.xml" qualifiers="" type="layout"/><file name="activity_scan_result" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_scan_result.xml" qualifiers="" type="layout"/><file name="activity_uuid" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_uuid.xml" qualifiers="" type="layout"/><file name="dialog_cache_size_setting" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\dialog_cache_size_setting.xml" qualifiers="" type="layout"/><file name="fragment_home" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_protection" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\fragment_protection.xml" qualifiers="" type="layout"/><file name="fragment_scan" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\fragment_scan.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_virus_database" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\fragment_virus_database.xml" qualifiers="" type="layout"/><file name="item_list" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\item_list.xml" qualifiers="" type="layout"/><file name="item_scan_record" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\item_scan_record.xml" qualifiers="" type="layout"/><file name="item_scan_result" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\item_scan_result.xml" qualifiers="" type="layout"/><file name="item_threat_detail" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\item_threat_detail.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="D:\Projects\Android\cccj-sdk\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Android\cccj-sdk\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Projects\Android\cccj-sdk\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="update_frequency_options">
        <item>每天</item>
        <item>每周</item>
        <item>每月</item>
    </string-array></file><file path="D:\Projects\Android\cccj-sdk\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary">#4285F4</color><color name="primary_variant">#1A73E8</color><color name="secondary">#E3F2FD</color><color name="black">#000000</color><color name="white">#FFFFFF</color><color name="gray">#757575</color><color name="light_gray">#F5F5F5</color><color name="success_green">#4CAF50</color><color name="safe_green">#4CAF50</color><color name="warning_yellow">#FFC107</color><color name="danger_red">#F44336</color><color name="processing_blue">#4285F4</color><color name="light_blue">#E3F2FD</color><color name="background_gray">#F5F5F5</color><color name="divider">#E0E0E0</color><color name="purple_500">#FF6200EE</color><color name="background_light">#FAFAFA</color></file><file path="D:\Projects\Android\cccj-sdk\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">安天卫士</string><string name="about">关于</string></file><file path="D:\Projects\Android\cccj-sdk\app\src\main\res\values\styles.xml" qualifiers=""><style name="QuickFunctionCard">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
    </style><style name="QuickFunctionLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">vertical</item>
        <item name="android:padding">16dp</item>
    </style><style name="QuickFunctionIcon">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">@drawable/circle_background_light_blue</item>
        <item name="android:padding">6dp</item>
        <item name="android:tint">#4285F4</item>
    </style><style name="QuickFunctionText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:textColor">#000000</item>
        <item name="android:textSize">12sp</item>
    </style></file><file path="D:\Projects\Android\cccj-sdk\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Demo" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.Demo" parent="Base.Theme.Demo"/><style name="Theme.AntiVirus" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/white</item>
        
        
        <item name="colorSecondary">@color/primary</item>
        <item name="colorSecondaryVariant">@color/primary_variant</item>
        <item name="colorOnSecondary">@color/white</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>

        
        <item name="bottomNavigationStyle">@style/Widget.App.BottomNavigationView</item>
    </style><style name="Widget.App.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemIconTint">@color/bottom_nav_colors</item>
        <item name="itemTextColor">@color/bottom_nav_colors</item>
    </style><style name="Theme.About" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_variant</item>
        <item name="android:statusBarColor">@color/primary_variant</item>
    </style></file><file path="D:\Projects\Android\cccj-sdk\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Demo" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\Projects\Android\cccj-sdk\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Projects\Android\cccj-sdk\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="bg_info_card" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\bg_info_card.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_list_empty" path="D:\Projects\Android\cccj-sdk\app\src\main\res\drawable\ic_list_empty.xml" qualifiers="" type="drawable"/><file name="activity_black_white_list_management" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\activity_black_white_list_management.xml" qualifiers="" type="layout"/><file name="dialog_add_black_white_item" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\dialog_add_black_white_item.xml" qualifiers="" type="layout"/><file name="item_black_white_list" path="D:\Projects\Android\cccj-sdk\app\src\main\res\layout\item_black_white_list.xml" qualifiers="" type="layout"/></source><source path="D:\Projects\Android\cccj-sdk\app\build\generated\res\rs\greatwall\debug"/><source path="D:\Projects\Android\cccj-sdk\app\build\generated\res\resValues\greatwall\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="greatwall$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Android\cccj-sdk\app\src\greatwall\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="greatwall" generated-set="greatwall$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Android\cccj-sdk\app\src\greatwall\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Android\cccj-sdk\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Android\cccj-sdk\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="greatwallDebug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Android\cccj-sdk\app\src\greatwallDebug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="greatwallDebug" generated-set="greatwallDebug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Android\cccj-sdk\app\src\greatwallDebug\res"/></dataSet><mergedItems/></merger>