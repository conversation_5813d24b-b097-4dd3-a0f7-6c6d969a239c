<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_black_white_list_management" modulePackage="com.antiy.demo" filePath="app\src\main\res\layout\activity_black_white_list_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_black_white_list_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="154" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="14" startOffset="8" endLine="21" endOffset="55"/></Target><Target id="@+id/tvStatistics" view="TextView"><Expressions/><location startLine="57" startOffset="20" endLine="64" endOffset="49"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="84" startOffset="20" endLine="90" endOffset="72"/></Target><Target id="@+id/emptyView" view="LinearLayout"><Expressions/><location startLine="93" startOffset="20" endLine="126" endOffset="34"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="129" startOffset="20" endLine="134" endOffset="54"/></Target><Target id="@+id/fabAdd" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="145" startOffset="4" endLine="152" endOffset="42"/></Target></Targets></Layout>