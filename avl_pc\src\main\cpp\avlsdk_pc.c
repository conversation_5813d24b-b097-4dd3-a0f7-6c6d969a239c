/************************************************************************\
                                                        FILE INFORMATION
                        ==================================================
                        Creator			:	PieroLsl <<EMAIL>>
                        Create time		:	2013-06-07 11:35:33
                        Company			:	Antiy Labs  (Harbin, China)
                        Location		:	Harbin, China

                                                                        LEGAL
                        ==================================================
                        Antiy Labs All rights reserved

                                                        FILE DESCRIPTION
                        ==================================================
                        The demonstration code, shows out the way of how to
                        use the APIs of AVLSDK3.0.
                        TAB = 4 space
\************************************************************************/

#include <dlfcn.h>
#include <errno.h>
#include <fcntl.h>
#include <getopt.h>
#include <jni.h>
#include <malloc.h>
#include <stdio.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#include "avlsdk_pc.h"

#define LOG_TAG "avlsdk_pc"

static const struct option options[]               = {{"file", required_argument, NULL, 'f'},   //	扫描文件
                                                      {"conf", required_argument, NULL, 'c'},   //	配置文件
                                                      {"help", no_argument, NULL, 'h'},         //	帮助
                                                      {NULL, 0, NULL, 0}};
static const int           CFG_FLAG_DETECT_ARRAY[] = {
    CFG_FLAG_SFX_DETECT_ENABLE,    CFG_FLAG_INFECT_DETECT_ENABLE,     CFG_FLAG_COMM_DETECT_ENABLE,       CFG_FLAG_SEC_DETECT_ENABLE,
    CFG_FLAG_PEP_DETECT_ENABLE,    CFG_FLAG_HD_DETECT_ENABLE,         CFG_FLAG_STREAM_DETECT_ENABLE,     CFG_FLAG_EXPLOIT_DETECT_ENABLE,
    CFG_FLAG_SCRIPT_DETECT_ENABLE, CFG_FLAG_FBLOOM_DETECT_ENABLE,     CFG_FLAG_KEXPLOIT_DETECT_ENABLE,   CFG_FLAG_VM_DETECT_ENABLE,
    CFG_FLAG_CLOUD_DETECT_ENABLE,  CFG_FLAG_BOL_DETECT_ENABLE,        CFG_FLAG_TXT_INFECT_DETECT_ENABLE, CFG_FLAG_ELF_DETECT_ENABLE,
    CFG_FLAG_YARA_DETECT_ENABLE,   CFG_FLAG_MACRO_DETECT_ENABLE,      CFG_FLAG_ANDROID_DETECT_ENABLE,    CFG_FLAG_GEN_HASH_DETECT_ENABLE,
    CFG_FLAG_NSIS_DETECT_ENABLE,   CFG_FLAG_SWF_DETECT_ENABLE,        CFG_FLAG_HEML_DETECT_ENABLE,       CFG_FLAG_REG_DETECT_ENABLE,
    CFG_FLAG_GSCPT_DETECT_ENABLE,  CFG_FLAG_ELF_SFX_DETECT_ENABLE,    CFG_FLAG_DOH_DETECT_ENABLE,        CFG_FLAG_VH_DETECT_ENABLE,
    CFG_FLAG_VC_DETECT_ENABLE,     CFG_FLAG_ANDROID_EX_DETECT_ENABLE, CFG_FLAG_IMP_DETECT_ENABLE,        CFG_FLAG_HMACRO_DETECT_ENABLE,
    CFG_FLAG_DC_DETECT_ENABLE,     CFG_FLAG_FINFO_DETECT_ENABLE,
};

static EngineHandler mEngineHandler;

long func_long_query_continue_callback(void* p_param)
{
        // This is the code sample, so it returns unconditionally. Users can modify according to the condition.
        return OD_CONTINUE;
}

long func_long_get_rslt_callback(P_OBJ_PROVIDER p_op, void* p_data, void* p_param)
{
        long           long_ret = 0, long_malware_id = 0, long_qry_ret = 0;
        P_ENGINE_PARAM p_ep        = (P_ENGINE_PARAM)p_param;
        unsigned char *puchar_desc = NULL, *puchar_analyser = NULL, auchar_malware_name[128] = {0};

        if (p_data == NULL || p_param == NULL) {
                long_ret = -1;
                goto GRC_OUT;
        }

        // Query the Malware ID
        long_qry_ret = p_ep->p_query_rpt_int(p_ep->p_engine, p_data, RPT_IDX_MALWARE_ID, &long_malware_id);
        if (long_qry_ret == ERR_RPT_NOT_EXIST) { goto GRC_OUT; }
        else if (long_qry_ret < 0) {
                LOGD("%s", "Query Malware_ID failed...");
                goto GRC_OUT;
        }

        // Query the analyser who detected this malware, users will use it when they need to get the malware name
        long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_ANALYSER, &puchar_analyser);
        if (long_qry_ret != ERR_SUCCESS) {
                printf("Query Analyser failed...\n");
                LOGD("%s", "Query Analyser failed...");
                goto GRC_OUT;
        }

        // Query the VName
        long_qry_ret = p_ep->p_query_name(
            p_ep->p_i2n_handle, (char*)puchar_analyser, long_malware_id, (unsigned char*)auchar_malware_name, sizeof(auchar_malware_name) - 1);
        if (long_qry_ret < 0) {
                printf("Query Malware_Name failed...\n");
                LOGD("%s", "Query Malware_Name failed...");
                goto GRC_OUT;
        }

        // Query current description about the object
        long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_OBJ_DESCRIPTION, &puchar_desc);
        if (long_qry_ret != ERR_SUCCESS) {
                printf("Query Desc failed...\n");
                LOGD("%s", "Query Desc failed...");
                goto GRC_OUT;
        }

        printf("Found malware : %lx\t\t%s\t\t%s\t\t%s\n", long_malware_id, (char*)auchar_malware_name, (char*)puchar_analyser, (char*)puchar_desc);

        LOGD("Found malware : %lx\t\t%s\t\t%s\t\t%s", long_malware_id, (char*)auchar_malware_name, (char*)puchar_analyser, (char*)puchar_desc);

GRC_OUT:
        return long_ret;
}

void func_void_show_usage()
{
        printf("\t\t--file   -f : scan the path of target file.\n");
        printf("\t\t--conf   -c : config file.\n");
        printf("\t\t--help   -h : show this message.\n");
}

int main0(int argc, char* argv[])
{
        long                         long_last_ret   = 0;
        void *                       p_engine_handle = NULL, *p_i2n_handle = NULL;
        void *                       p_sdk_handle = NULL, *p_name_handle = NULL;
        P_AVL_SDK_Create             p_create         = NULL;
        P_AVL_SDK_Release            p_release        = NULL;
        P_AVL_SDK_Init               p_init           = NULL;
        P_AVL_SDK_LoadConfigFile     p_config         = NULL;
        P_AVL_SDK_SetConfigInt       p_set_cfg_int    = NULL;
        P_AVL_SDK_SetConfigString    p_set_cfg_str    = NULL;
        P_AVL_SDK_Scan               p_scan           = NULL;
        P_AVL_SDK_QueryReportInt     p_query_rpt_int  = NULL;
        P_AVL_SDK_QueryReportStr     p_query_rpt_str  = NULL;
        P_AVL_SDK_ReloadDB           p_reload         = NULL;
        P_AVL_SDK_QueryDBInfo        p_query_db_info  = NULL;
        P_AVL_NTranser_Init          p_i2n_init       = NULL;
        P_AVL_NTranser_Release       p_i2n_release    = NULL;
        P_AVL_NTranser_QueryNameByID p_i2n_query_name = NULL;
        char *                       pchar_scan_file = NULL, *pchar_conf_file = NULL;
        int                          int_c = 0, i = 0;

        printf("\t\tAGB for consol v1.0.1.1 by Antiy Labs\n");

        while ((int_c = getopt_long(argc, argv, "c:hf:", options, NULL)) >= 0) {
                switch (int_c) {
                        case 'f': pchar_scan_file = optarg; break;
                        case 'c': pchar_conf_file = optarg; break;
                        case 'h': func_void_show_usage(); return 0;
                }
        }

        if (pchar_conf_file == NULL) { goto MAIN_OUT; }


        // Load the SDK so
        p_sdk_handle = dlopen(DEFAULT_SDK_PATH, RTLD_LAZY);
        if (p_sdk_handle == NULL) {
                printf("Load SDK failed...\n");
                goto MAIN_OUT;
        }

        // Load the NameTranser so
        p_name_handle = dlopen(DEFAULT_ID2NAME_PATH, RTLD_LAZY);
        if (p_name_handle == NULL) {
                printf("Load ID2 failed...\n");
                goto MAIN_OUT;
        }

        // Get APIs
        p_create        = dlsym(p_sdk_handle, "AVL_SDK_CreateInstance");
        p_release       = dlsym(p_sdk_handle, "AVL_SDK_Release");
        p_init          = dlsym(p_sdk_handle, "AVL_SDK_InitInstance");
        p_config        = dlsym(p_sdk_handle, "AVL_SDK_LoadConfigFile");
        p_set_cfg_int   = dlsym(p_sdk_handle, "AVL_SDK_SetConfigInt");
        p_set_cfg_str   = dlsym(p_sdk_handle, "AVL_SDK_SetConfigString");
        p_scan          = dlsym(p_sdk_handle, "AVL_SDK_Scan");
        p_query_rpt_int = dlsym(p_sdk_handle, "AVL_SDK_QueryReportInt");
        p_query_rpt_str = dlsym(p_sdk_handle, "AVL_SDK_QueryReportStr");
        p_reload        = dlsym(p_sdk_handle, "AVL_SDK_ReloadDB");
        p_query_db_info = dlsym(p_sdk_handle, "AVL_SDK_QueryDBInfo");

        if (p_create == NULL || p_release == NULL || p_init == NULL || p_config == NULL || p_set_cfg_int == NULL || p_set_cfg_str == NULL || p_scan == NULL ||
            p_query_rpt_int == NULL || p_query_rpt_str == NULL || p_reload == NULL) {
                printf("Get procs failed...\n");
                goto MAIN_OUT;
        }

        p_i2n_init       = dlsym(p_name_handle, "AVL_NTranser_Init");
        p_i2n_release    = dlsym(p_name_handle, "AVL_NTranser_Release");
        p_i2n_query_name = dlsym(p_name_handle, "AVL_NTranser_QueryNameByID");

        if (p_i2n_init == NULL || p_i2n_release == NULL || p_i2n_query_name == NULL) {
                printf("Get procs failed...\n");
                goto MAIN_OUT;
        }

START:
        // Create a new instance of AVLSDK
        long_last_ret = p_create(&p_engine_handle);
        if (long_last_ret != ERR_SUCCESS) {
                printf("Create failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }

        // Load the configuration template
        long_last_ret = p_config(p_engine_handle, pchar_conf_file);
        if (long_last_ret != ERR_SUCCESS) {
                printf("LoadConfig failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }

        // Set the configuration by users
        long_last_ret = p_set_cfg_int(p_engine_handle, CFG_INT_APACK_RECURE_LAYER, 5);
        if (long_last_ret != ERR_SUCCESS) {
                printf("SetConfig failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }

        // Set the configuration by users
        long_last_ret = p_set_cfg_str(p_engine_handle, CFG_STR_LICENSE_PATH, "./License.alf");
        if (long_last_ret != ERR_SUCCESS) {
                printf("SetConfig failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }

        // Initialize the instance
        long_last_ret = p_init(p_engine_handle, NULL);
        if (long_last_ret != ERR_SUCCESS) {
                printf("Init failed : %d\n", (int)long_last_ret);
                p_engine_handle = NULL;
                goto MAIN_OUT;
        }

        // Initialize the NameTranser instance
        long_last_ret = p_i2n_init(DEFAULT_ID2N_DB_PATH, &p_i2n_handle);
        if (long_last_ret != 0) {
                printf("Init NameTranser failed : %d\n", (int)long_last_ret);
                goto MAIN_OUT;
        }

SCAN:
        // Scan
        if (pchar_scan_file != NULL) {
                unsigned char* puchar_buf      = NULL;
                unsigned long  ulong_file_size = 0;
                int            fd              = 0;
                struct stat    status          = {0};
                OBJ_PROVIDER   op              = {0};
                OBJ_DISPOSER   od              = {0};
                ENGINE_PARAM   ep              = {0};

                fd = open(pchar_scan_file, O_RDONLY);
                if (fd != -1) {
                        lstat(pchar_scan_file, &status);
                        ulong_file_size = status.st_size;
                        puchar_buf      = (unsigned char*)mmap(NULL, ulong_file_size, PROT_READ, MAP_SHARED, fd, 0);
                }
                printf("Scan %s...\n", pchar_scan_file);

                // Initialize the OBJ_PROVIDER structure
                op.obj_ver   = CUR_ENGINE_VER;
                op.evro_type = ET_DESKTOP;
                op.buf       = puchar_buf;
                op.size      = ulong_file_size;
                strncpy((char*)op.obj_des, pchar_scan_file, sizeof(op.obj_des));

                // Initialize the ENGINE_PARAM structure, it will be used by report callback
                ep.p_engine        = p_engine_handle;
                ep.p_i2n_handle    = p_i2n_handle;
                ep.p_query_rpt_int = p_query_rpt_int;
                ep.p_query_rpt_str = p_query_rpt_str;
                ep.p_query_name    = p_i2n_query_name;

                // Initialize the OBJ_DISPOSERR structure
                od.rpt_callback            = func_long_get_rslt_callback;
                od.p_rpt_param             = &ep;
                od.query_continue_callback = func_long_query_continue_callback;
                od.p_qc_param              = NULL;

                long_last_ret = p_scan(p_engine_handle, &op, &od);
                if (long_last_ret < 0) { printf("Scan failed...\n"); }

                if (puchar_buf != NULL) {
                        munmap(puchar_buf, ulong_file_size);
                        puchar_buf = NULL;
                }

                if (fd != -1) { close(fd); }
        }

#if 0
    for (; i < 3; i++)
    {
        if (p_reload(p_engine_handle) != ERR_SUCCESS)
        {
            printf("Reload failed...\n");
        }
        else
        {
            i++;
            goto	SCAN;
        }
    }
#endif

        // Release the AVLSDK instance and the so...
MAIN_OUT:
        if (p_engine_handle != NULL) {
                p_release(p_engine_handle);
                p_engine_handle = NULL;
        }

        if (p_i2n_handle != NULL) {
                p_i2n_release(p_i2n_handle);
                p_i2n_handle = NULL;
        }

#if 0
    for (; i < 3000; i++)
    {
        printf("goto %d\n", i);
        i++;
        getchar();
        goto	START;
    }
#endif

        if (p_sdk_handle != NULL) { dlclose(p_sdk_handle); }

        if (p_name_handle != NULL) { dlclose(p_name_handle); }


        return 0;
}

static int __attribute__((visibility("hidden"))) jniRegisterNativeMethods(JNIEnv* env, jclass cls, const JNINativeMethod* gMethods, int numMethods)
{
        if (cls == NULL) { return -1; }

        int result = 0;
        if ((*env)->RegisterNatives(env, cls, gMethods, numMethods) < 0) { result = -1; }

        (*env)->DeleteLocalRef(env, cls);
        return result;
}

long my_func_long_get_rslt_callback(P_OBJ_PROVIDER p_op, void* p_data, void* p_param)
{
        long           long_ret = 0, long_malware_id = 0, long_qry_ret = 0;
        P_ENGINE_PARAM p_ep        = (P_ENGINE_PARAM)p_param;
        unsigned char *puchar_desc = NULL, *puchar_analyser = NULL, *puchar_md5 = NULL, auchar_malware_name[128] = {0};

        if (p_data == NULL || p_param == NULL) {
                long_ret = -1;
                goto GRC_OUT;
        }

        // Query md5 of result object
        long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_OBJ_MD5, &puchar_md5);
        if (long_qry_ret != ERR_SUCCESS) {
                printf("Query MD5 failed...\n");
                LOGD("%s", "Query MD5 failed...");
                goto GRC_OUT;
        }
        memcpy(mEngineHandler.pScanResult.md5, puchar_md5, sizeof(mEngineHandler.pScanResult.md5));

        // Query the Malware ID
        long_qry_ret = p_ep->p_query_rpt_int(p_ep->p_engine, p_data, RPT_IDX_MALWARE_ID, &long_malware_id);
        if (long_qry_ret == ERR_RPT_NOT_EXIST) { goto GRC_OUT; }
        else if (long_qry_ret < 0) {
                LOGD("%s", "Query Malware_ID failed...");
                goto GRC_OUT;
        }

        // Query the analyser who detected this malware, users will use it when they need to get the malware name
        long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_ANALYSER, &puchar_analyser);
        if (long_qry_ret != ERR_SUCCESS) {
                printf("Query Analyser failed...\n");
                LOGD("%s", "Query Analyser failed...");
                goto GRC_OUT;
        }

        // Query the VName
        long_qry_ret = p_ep->p_query_name(p_ep->p_i2n_handle, (char*)puchar_analyser, long_malware_id, auchar_malware_name, sizeof(auchar_malware_name) - 1);
        if (long_qry_ret < 0) {
                printf("Query Malware_Name failed...\n");
                LOGD("%s", "Query Malware_Name failed...");
                goto GRC_OUT;
        }

        // Query current description about the object
        long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_OBJ_DESCRIPTION, &puchar_desc);
        if (long_qry_ret != ERR_SUCCESS) {
                printf("Query Desc failed...\n");
                LOGD("%s", "Query Desc failed...");
                goto GRC_OUT;
        }

        printf("Found malware : %lx\t\t%s\t\t%s\t\t%s\n", long_malware_id, (char*)auchar_malware_name, (char*)puchar_analyser, (char*)puchar_desc);
        LOGD("Found malware(desc) with MD5: %lx\t\t%s(%s)\t\t%s", long_malware_id, (char*)auchar_malware_name, (char*)puchar_desc, (char*)puchar_md5);

        strcpy(mEngineHandler.pScanResult.malwareName, (char*)auchar_malware_name);
        strcpy(mEngineHandler.pScanResult.description, (char*)puchar_desc);

GRC_OUT:
        return long_ret;
}

long native_scan(const char* p_filepath, ScanResult* result)
{
        if (!result) {
                LOGD("%s: param(result) is null", __func__);
                return -1;
        }

        long long_last_ret = 0;

        if (mEngineHandler.pengine_handle == NULL) { LOGE("%s: engine not initialized", __func__); }

        memset(&mEngineHandler.pScanResult, 0, sizeof(mEngineHandler.pScanResult));

        if (p_filepath != NULL) {
                unsigned char* puchar_buf      = NULL;
                unsigned long  ulong_file_size = 0;
                int            fd              = 0;
                struct stat    status          = {0};
                OBJ_PROVIDER   op              = {0};
                OBJ_DISPOSER   od              = {0};
                ENGINE_PARAM   ep              = {0};

                fd = open(p_filepath, O_RDONLY);
                if (fd != -1) {
                        lstat(p_filepath, &status);
                        ulong_file_size = status.st_size;
                        puchar_buf      = (unsigned char*)mmap(NULL, ulong_file_size, PROT_READ, MAP_SHARED, fd, 0);
                }
                LOGD("Scan %s...", p_filepath);

                // Initialize the OBJ_PROVIDER structure
                op.obj_ver   = CUR_ENGINE_VER;
                op.evro_type = ET_DESKTOP;
                op.buf       = puchar_buf;
                op.size      = ulong_file_size;
                strncpy((char*)op.obj_des, p_filepath, MIN(sizeof(op.obj_des), strlen(p_filepath)));

                // Initialize the ENGINE_PARAM structure, it will be used by report callback
                ep.p_engine        = mEngineHandler.pengine_handle;
                ep.p_i2n_handle    = mEngineHandler.p_i2n_handle;
                ep.p_query_rpt_int = mEngineHandler.p_query_rpt_int;
                ep.p_query_rpt_str = mEngineHandler.p_query_rpt_str;
                ep.p_query_name    = mEngineHandler.p_i2n_query_name;

                // Initialize the OBJ_DISPOSERR structure
                od.rpt_callback            = my_func_long_get_rslt_callback;
                od.p_rpt_param             = &ep;
                od.query_continue_callback = func_long_query_continue_callback;
                od.p_qc_param              = NULL;

                long_last_ret = mEngineHandler.p_scan(mEngineHandler.pengine_handle, &op, &od);
                LOGD("%s: scan done.", __func__);
                if (long_last_ret < 0) { LOGD("%s: scan failed(%ld)", __func__, long_last_ret); }
                else {
                        // 根据全局引擎中保存的扫描结果，填充内容。
                        memcpy(result, &mEngineHandler.pScanResult, sizeof(ScanResult));
                        LOGD("%s", "get result done.");
                }
                if (puchar_buf != NULL) {
                        munmap(puchar_buf, ulong_file_size);
                        puchar_buf = NULL;
                }

                if (fd != -1) { close(fd); }
        }

        return long_last_ret;
}

// get db version
JNIEXPORT jstring Java_com_antiy_avlsdk_pc_AVLEnginePC_getDbInfo(JNIEnv* env, jclass type)
{
        jstring ret = (*env)->NewStringUTF(env, "");

        if (mEngineHandler.pengine_handle != NULL) {
                P_DB_INFO info;
                info = calloc(1, sizeof(*info));
                mEngineHandler.p_query_db_info(mEngineHandler.pengine_handle, info);

                LOGD("Current db ver: %s", info->db_time_stamp);

                if (strlen(info->db_time_stamp) != 0) ret = (*env)->NewStringUTF(env, info->db_time_stamp);
        }

        return ret;
}

JNIEXPORT jstring Java_com_antiy_avlsdk_pc_AVLEnginePC_getCurVersion(JNIEnv* env, jclass type)
{
        jstring ret = (*env)->NewStringUTF(env, "");

        char version[128] = {'\0'};
        mEngineHandler.p_get_cur_version(version, 128);

        if (version[0] != '\0') { ret = (*env)->NewStringUTF(env, version); }

        return ret;
}

JNIEXPORT jstring Java_com_antiy_avlsdk_pc_AVLEnginePC_scan(JNIEnv* env, jclass type, jstring filePath)
{
        long       long_last_ret   = 0;
        char*      pchar_scan_file = NULL;
        ScanResult scanResult;
        jstring    result;

        if (mEngineHandler.pengine_handle == NULL) { return NULL; }
        pchar_scan_file = (char*)(*env)->GetStringUTFChars(env, filePath, 0);

        memset(&scanResult, 0, sizeof(scanResult));

        long_last_ret = native_scan(pchar_scan_file, &scanResult);
        if (long_last_ret < 0) { result = (*env)->NewStringUTF(env, ""); }
        else {
                result = (*env)->NewStringUTF(env, scanResult.malwareName);
        }
        return result;
}

JNIEXPORT jobject JNICALL Java_com_antiy_avlsdk_pc_AVLEnginePC_scanWithMd5(JNIEnv* env, jobject thiz, jstring filePath)
{
        long       long_last_ret   = 0;
        char*      pchar_scan_file = NULL;
        ScanResult scanResult;

        // for return
        jstring    name;
        jbyteArray md5;
        jobject    result;

        // 获取 Pair 类和方法 ID
        jclass pairClass = (*env)->FindClass(env, "android/util/Pair");
        if (!pairClass) {
                // 处理错误：类未找到
                LOGE("%s: cannot find class android.util.Pair", __func__);
                return NULL;
        }

        // 获取构造方法签名 (Ljava/lang/Object;Ljava/lang/Object;)V
        jmethodID constructor = (*env)->GetMethodID(env, pairClass, "<init>", "(Ljava/lang/Object;Ljava/lang/Object;)V");
        if (!constructor) {
                // 处理错误：构造方法未找到
                LOGE("%s: cannot find initialization method for android.util.Pair", __func__);
                return NULL;
        }

        if (mEngineHandler.pengine_handle == NULL) { return NULL; }
        pchar_scan_file = (char*)(*env)->GetStringUTFChars(env, filePath, 0);

        memset(&scanResult, 0, sizeof(scanResult));

        long_last_ret = native_scan(pchar_scan_file, &scanResult);
        if (long_last_ret < 0) {
                name = (*env)->NewStringUTF(env, "");
                md5  = (*env)->NewByteArray(env, 0);
        }
        else {
                name = (*env)->NewStringUTF(env, scanResult.malwareName);
                md5  = (*env)->NewByteArray(env, 16);
                (*env)->SetByteArrayRegion(env, md5, 0, 16, (signed char*)scanResult.md5);
        }

        result = (*env)->NewObject(env, pairClass, constructor, name, md5);

        (*env)->DeleteLocalRef(env, name);
        (*env)->DeleteLocalRef(env, md5);
        (*env)->DeleteLocalRef(env, pairClass);

        return result;
}

JNIEXPORT jint Java_com_antiy_avlsdk_pc_AVLEnginePC_loadEngine(JNIEnv* env, jclass type, jstring filesDir)
{
        long  long_last_ret = 0;
        void *p_sdk_handle = NULL, *p_name_handle = NULL;

        memset(&mEngineHandler, 0, sizeof(mEngineHandler));

        char* pchar_conf_file = NULL;

        pchar_conf_file = (char*)(*env)->GetStringUTFChars(env, filesDir, 0);

        char fileBuf[256] = {
            0,
        };
        sprintf(fileBuf, "%s/%s", pchar_conf_file, DEFAULT_LOAD_CONFIG);

        char avl_sdk_path[256] = {
            0,
        };
        sprintf(avl_sdk_path, "%s/%s", pchar_conf_file, DEFAULT_SDK_PATH);
        LOGD("AVLSDK.so path: %s", avl_sdk_path);

        char avl_id2name_path[256] = {
            0,
        };
        sprintf(avl_id2name_path, "%s/%s", pchar_conf_file, DEFAULT_ID2NAME_PATH);
        LOGD("id2name.so path: %s", avl_id2name_path);

        //    LOGD("\t\tAGB for consol v1.0.1.1 by Antiy Labs\n", "")
        // Load the SDK so
        p_sdk_handle = dlopen(avl_sdk_path, RTLD_LAZY);
        if (p_sdk_handle == NULL) {
                LOGD("Load SDK failed... %s\n", dlerror());
                return -1;
        }

        // Load the NameTranser so
        p_name_handle = dlopen(avl_id2name_path, RTLD_LAZY);
        if (p_name_handle == NULL) {
                LOGD("%s", "Load SDK failed...\n");
                return -1;
        }

        // Get APIs
        mEngineHandler.p_create          = dlsym(p_sdk_handle, "AVL_SDK_CreateInstance");
        mEngineHandler.p_release         = dlsym(p_sdk_handle, "AVL_SDK_Release");
        mEngineHandler.p_init            = dlsym(p_sdk_handle, "AVL_SDK_InitInstance");
        mEngineHandler.p_config          = dlsym(p_sdk_handle, "AVL_SDK_LoadConfigFile");
        mEngineHandler.p_set_cfg_int     = dlsym(p_sdk_handle, "AVL_SDK_SetConfigInt");
        mEngineHandler.p_set_cfg_str     = dlsym(p_sdk_handle, "AVL_SDK_SetConfigString");
        mEngineHandler.p_scan            = dlsym(p_sdk_handle, "AVL_SDK_Scan");
        mEngineHandler.p_query_rpt_int   = dlsym(p_sdk_handle, "AVL_SDK_QueryReportInt");
        mEngineHandler.p_query_rpt_str   = dlsym(p_sdk_handle, "AVL_SDK_QueryReportStr");
        mEngineHandler.p_reload          = dlsym(p_sdk_handle, "AVL_SDK_ReloadDB");
        mEngineHandler.p_query_db_info   = dlsym(p_sdk_handle, "AVL_SDK_QueryDBInfo");
        mEngineHandler.p_get_cur_version = dlsym(p_sdk_handle, "AVL_SDK_GetCurVersion");


        if (mEngineHandler.p_create == NULL || mEngineHandler.p_release == NULL || mEngineHandler.p_init == NULL || mEngineHandler.p_config == NULL ||
            mEngineHandler.p_set_cfg_int == NULL || mEngineHandler.p_set_cfg_str == NULL || mEngineHandler.p_scan == NULL ||
            mEngineHandler.p_query_rpt_int == NULL || mEngineHandler.p_query_rpt_str == NULL || mEngineHandler.p_reload == NULL ||
            mEngineHandler.p_query_db_info == NULL || mEngineHandler.p_get_cur_version == NULL) {
                LOGD("%s", "Get procs failed...\n");
                return -1;
        }

        mEngineHandler.p_i2n_init       = dlsym(p_name_handle, "AVL_NTranser_Init");
        mEngineHandler.p_i2n_release    = dlsym(p_name_handle, "AVL_NTranser_Release");
        mEngineHandler.p_i2n_query_name = dlsym(p_name_handle, "AVL_NTranser_QueryNameByID");

        if (mEngineHandler.p_i2n_init == NULL || mEngineHandler.p_i2n_release == NULL || mEngineHandler.p_i2n_query_name == NULL) {
                LOGD("%s", "Get procs failed...\n");
                return -1;
        }

START:
        // Create a new instance of AVLSDK
        long_last_ret = mEngineHandler.p_create(&mEngineHandler.pengine_handle);
        if (long_last_ret != ERR_SUCCESS) {
                LOGD("Create failed : %d\n", (int)long_last_ret);
                return long_last_ret;
        }

        // Load the configuration template
        long_last_ret = mEngineHandler.p_config(mEngineHandler.pengine_handle, fileBuf);
        if (long_last_ret != ERR_SUCCESS) {
                LOGD("LoadConfig failed : %d\n", (int)long_last_ret);
                return long_last_ret;
        }

        // 开启日志调试
        mEngineHandler.p_set_cfg_int(mEngineHandler.pengine_handle, CFG_FLAG_LOG_ENABLE, 0);
        mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, CFG_STR_LOG_PATH, pchar_conf_file);

        memset(fileBuf, 0, sizeof(fileBuf));
        sprintf(fileBuf, "%s/%s", pchar_conf_file, "Data");
        mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, CFG_STR_DATA_PATH, fileBuf);

        memset(fileBuf, 0, sizeof(fileBuf));
        sprintf(fileBuf, "%s/%s", pchar_conf_file, "Dam");
        LOGD("Dam path %s", fileBuf);
        mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, 316, fileBuf);

        memset(fileBuf, 0, sizeof(fileBuf));
        sprintf(fileBuf, "%s/%s", pchar_conf_file, "Module");
        LOGD("module path %s", fileBuf);
        mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, CFG_STR_MODULE_PATH, fileBuf);

        // Set the configuration by users
        long_last_ret = mEngineHandler.p_set_cfg_int(mEngineHandler.pengine_handle, CFG_INT_APACK_RECURE_LAYER, 5);
        if (long_last_ret != ERR_SUCCESS) {
                LOGD("SetConfig failed : %d\n", (int)long_last_ret);
                return long_last_ret;
        }

        // Set the configuration by users
        memset(fileBuf, 0, sizeof(fileBuf));
        sprintf(fileBuf, "%s/%s", pchar_conf_file, "License.alf");
        LOGD("license file %s", fileBuf);
        //    long_last_ret = p_set_cfg_str(p_engine_handle, CFG_STR_LICENSE_PATH, "License.alf");
        long_last_ret = mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, CFG_STR_LICENSE_PATH, fileBuf);
        if (long_last_ret != ERR_SUCCESS) {
                LOGD("SetConfig failed : %d\n", (int)long_last_ret);
                return long_last_ret;
        }

        // Initialize the instance
        long_last_ret = mEngineHandler.p_init(mEngineHandler.pengine_handle, NULL);
        if (long_last_ret != ERR_SUCCESS) {
                LOGD("Init failed : %d\n", (int)long_last_ret);
                mEngineHandler.pengine_handle = NULL;
                return long_last_ret;
        }

        // Initialize the NameTranser instance
        memset(fileBuf, 0, sizeof(fileBuf));
        sprintf(fileBuf, "%s/%s", pchar_conf_file, DEFAULT_ID2N_DB_PATH);
        LOGD("NData path %s", fileBuf);
        long_last_ret = mEngineHandler.p_i2n_init(fileBuf, &mEngineHandler.p_i2n_handle);
        if (long_last_ret != 0) {
                LOGD("Init NameTranser failed : %d\n", (int)long_last_ret);
                return long_last_ret;
        }

        LOGD("%s", "初始化成功");
        return 0;
}

JNIEXPORT void JNICALL Java_com_antiy_avlsdk_pc_AVLEnginePC_setScanEnabled(JNIEnv* env, jobject thiz, jboolean enabled)
{
        if (mEngineHandler.pengine_handle && mEngineHandler.p_i2n_handle) {
                if (enabled) {
                        // 启用所有扫描功能
                        for (int i = 0; i < sizeof(CFG_FLAG_DETECT_ARRAY); i++) {
                                mEngineHandler.p_set_cfg_int(mEngineHandler.pengine_handle, CFG_FLAG_DETECT_ARRAY[i], 1);
                        }
                }
                else {
                        // 停用所有扫描功能
                        for (int i = 0; i < sizeof(CFG_FLAG_DETECT_ARRAY); i++) {
                                mEngineHandler.p_set_cfg_int(mEngineHandler.pengine_handle, CFG_FLAG_DETECT_ARRAY[i], 0);
                        }
                }
        }
}

/**
 * 卸载引擎. 用于在更新时停止对引擎文件的占用.
 *
 * @param env jni函数固定参数
 * @param type jni函数固定参数
 */
JNIEXPORT void Java_com_antiy_avlsdk_pc_AVLEnginePC_unloadEngine(JNIEnv* env, jclass type)
{
        if (mEngineHandler.pengine_handle && mEngineHandler.p_release) { mEngineHandler.p_release(mEngineHandler.pengine_handle); }
        if (mEngineHandler.p_i2n_handle && mEngineHandler.p_i2n_release) { mEngineHandler.p_i2n_release(mEngineHandler.p_i2n_handle); }

        memset(&mEngineHandler, 0, sizeof(mEngineHandler));
}

JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) { return JNI_VERSION_1_4; }
