// Generated by view binder compiler. Do not edit!
package com.antiy.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.antiy.demo.R;
import com.google.android.material.chip.Chip;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBlackWhiteListBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageButton btnDelete;

  @NonNull
  public final Chip chipType;

  @NonNull
  public final TextView tvFullHash;

  @NonNull
  public final TextView tvHash;

  @NonNull
  public final TextView tvType;

  private ItemBlackWhiteListBinding(@NonNull CardView rootView, @NonNull ImageButton btnDelete,
      @NonNull Chip chipType, @NonNull TextView tvFullHash, @NonNull TextView tvHash,
      @NonNull TextView tvType) {
    this.rootView = rootView;
    this.btnDelete = btnDelete;
    this.chipType = chipType;
    this.tvFullHash = tvFullHash;
    this.tvHash = tvHash;
    this.tvType = tvType;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBlackWhiteListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBlackWhiteListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_black_white_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBlackWhiteListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnDelete;
      ImageButton btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.chipType;
      Chip chipType = ViewBindings.findChildViewById(rootView, id);
      if (chipType == null) {
        break missingId;
      }

      id = R.id.tvFullHash;
      TextView tvFullHash = ViewBindings.findChildViewById(rootView, id);
      if (tvFullHash == null) {
        break missingId;
      }

      id = R.id.tvHash;
      TextView tvHash = ViewBindings.findChildViewById(rootView, id);
      if (tvHash == null) {
        break missingId;
      }

      id = R.id.tvType;
      TextView tvType = ViewBindings.findChildViewById(rootView, id);
      if (tvType == null) {
        break missingId;
      }

      return new ItemBlackWhiteListBinding((CardView) rootView, btnDelete, chipType, tvFullHash,
          tvHash, tvType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
