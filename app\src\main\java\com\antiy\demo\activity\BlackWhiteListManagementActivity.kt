package com.antiy.demo.activity

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.storage.DataManager
import com.antiy.demo.adapter.BlackWhiteListAdapter
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityBlackWhiteListManagementBinding
import com.antiy.demo.databinding.DialogAddBlackWhiteItemBinding
import com.antiy.demo.entity.BlackWhiteListItem
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import java.util.concurrent.Executors

/**
 * 黑白名单管理页面
 * 提供黑白名单的查看、添加、删除功能
 */
class BlackWhiteListManagementActivity : BaseActivity<ActivityBlackWhiteListManagementBinding>() {

    companion object {
        private const val EXTRA_LIST_TYPE = "list_type"
        const val TYPE_WHITE_LIST = "white_list"
        const val TYPE_BLACK_LIST = "black_list"
        const val TYPE_ALL = "all"

        /**
         * 启动黑白名单管理页面
         * @param context 上下文
         * @param listType 列表类型：TYPE_WHITE_LIST, TYPE_BLACK_LIST, TYPE_ALL
         */
        fun start(context: Context, listType: String = TYPE_ALL) {
            val intent = Intent(context, BlackWhiteListManagementActivity::class.java)
            intent.putExtra(EXTRA_LIST_TYPE, listType)
            context.startActivity(intent)
        }
    }

    private lateinit var adapter: BlackWhiteListAdapter
    private var listType: String = TYPE_ALL
    private val blackWhiteListItems = mutableListOf<BlackWhiteListItem>()

    override fun getViewBinding(inflater: LayoutInflater): ActivityBlackWhiteListManagementBinding {
        return ActivityBlackWhiteListManagementBinding.inflate(inflater)
    }

    override fun initView() {
        super.initView()
        
        // 获取传入的列表类型
        listType = intent.getStringExtra(EXTRA_LIST_TYPE) ?: TYPE_ALL
        
        setupToolbar()
        setupRecyclerView()
        setupFab()
        loadBlackWhiteListData()
    }

    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        // 设置标题为黑白名单管理
        binding.toolbar.title = "黑白名单管理"
    }

    private fun setupRecyclerView() {
        adapter = BlackWhiteListAdapter(
            items = blackWhiteListItems,
            onDeleteClick = { item ->
                showDeleteConfirmDialog(item)
            },
            onItemClick = { item ->
                showItemDetailDialog(item)
            }
        )
        
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@BlackWhiteListManagementActivity)
            adapter = <EMAIL>
        }
    }

    private fun setupFab() {
        binding.fabAdd.setOnClickListener {
            showAddItemDialog()
        }
    }

    private fun loadBlackWhiteListData() {
        binding.progressBar.visibility = View.VISIBLE
        binding.emptyView.visibility = View.GONE

        val executor = Executors.newSingleThreadExecutor()
        val handler = Handler(Looper.getMainLooper())

        executor.execute {
            try {
                val data = DataManager.getInstance().blackWhiteListData

                handler.post {
                    updateListData(data)
                    binding.progressBar.visibility = View.GONE

                    if (blackWhiteListItems.isEmpty()) {
                        binding.emptyView.visibility = View.VISIBLE
                        binding.recyclerView.visibility = View.GONE
                    } else {
                        binding.emptyView.visibility = View.GONE
                        binding.recyclerView.visibility = View.VISIBLE
                    }
                }
            } catch (e: Exception) {
                handler.post {
                    binding.progressBar.visibility = View.GONE
                    Toast.makeText(this@BlackWhiteListManagementActivity,
                        "加载数据失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    AVLEngine.Logger.error("Failed to load black white list data: ${e.message}")
                }
            }
        }
    }

    private fun updateListData(data: HashMap<String, Boolean>) {
        blackWhiteListItems.clear()
        
        data.forEach { (hash, isWhite) ->
            // 根据列表类型过滤数据
            when (listType) {
                TYPE_WHITE_LIST -> if (isWhite) blackWhiteListItems.add(BlackWhiteListItem(hash, isWhite))
                TYPE_BLACK_LIST -> if (!isWhite) blackWhiteListItems.add(BlackWhiteListItem(hash, isWhite))
                else -> blackWhiteListItems.add(BlackWhiteListItem(hash, isWhite))
            }
        }
        
        // 按类型和hash排序
        blackWhiteListItems.sortWith(compareBy<BlackWhiteListItem> { !it.isWhite }.thenBy { it.hash })
        adapter.notifyDataSetChanged()
        
        // 更新统计信息
        updateStatistics()
    }

    private fun updateStatistics() {
        val whiteCount = blackWhiteListItems.count { it.isWhite }
        val blackCount = blackWhiteListItems.count { !it.isWhite }
        
        binding.tvStatistics.text = when (listType) {
            TYPE_WHITE_LIST -> "共 $whiteCount 个白名单项"
            TYPE_BLACK_LIST -> "共 $blackCount 个黑名单项"
            else -> "白名单: $whiteCount 个，黑名单: $blackCount 个"
        }
    }

    private fun showAddItemDialog() {
        val dialogBinding = DialogAddBlackWhiteItemBinding.inflate(layoutInflater)
        
        // 根据列表类型设置默认选择
        when (listType) {
            TYPE_WHITE_LIST -> {
                dialogBinding.radioWhite.isChecked = true
                dialogBinding.radioGroupType.visibility = View.GONE
                dialogBinding.tvTypeLabel.text = "类型: 白名单"
            }
            TYPE_BLACK_LIST -> {
                dialogBinding.radioBlack.isChecked = true
                dialogBinding.radioGroupType.visibility = View.GONE
                dialogBinding.tvTypeLabel.text = "类型: 黑名单"
            }
            else -> {
                dialogBinding.radioWhite.isChecked = true
                dialogBinding.radioGroupType.visibility = View.VISIBLE
                dialogBinding.tvTypeLabel.visibility = View.GONE
            }
        }
        
        val dialog = MaterialAlertDialogBuilder(this)
            .setTitle("添加黑白名单项")
            .setView(dialogBinding.root)
            .setPositiveButton("添加", null)
            .setNegativeButton("取消", null)
            .create()
        
        dialog.setOnShowListener {
            dialog.getButton(androidx.appcompat.app.AlertDialog.BUTTON_POSITIVE).setOnClickListener {
                val hash = dialogBinding.etHash.text.toString().trim()
                if (hash.isEmpty()) {
                    dialogBinding.etHash.error = "请输入文件Hash值"
                    return@setOnClickListener
                }
                
                if (hash.length < 32) {
                    dialogBinding.etHash.error = "Hash值长度不正确"
                    return@setOnClickListener
                }
                
                val isWhite = when (listType) {
                    TYPE_WHITE_LIST -> true
                    TYPE_BLACK_LIST -> false
                    else -> dialogBinding.radioWhite.isChecked
                }
                
                addBlackWhiteItem(hash, isWhite)
                dialog.dismiss()
            }
        }
        
        dialog.show()
    }

    private fun addBlackWhiteItem(hash: String, isWhite: Boolean) {
        val executor = Executors.newSingleThreadExecutor()
        val handler = Handler(Looper.getMainLooper())

        executor.execute {
            try {
                // 创建新的map包含新项
                val currentData = DataManager.getInstance().blackWhiteListData
                currentData[hash] = isWhite

                // 保存到数据库
                DataManager.getInstance().saveBlackWhiteList(currentData)

                // 更新配置
                AVLEngine.getInstance().updateConfig { config ->
                    config?.updateBlackWhiteList(currentData)
                }

                handler.post {
                    Toast.makeText(this@BlackWhiteListManagementActivity,
                        "添加${if (isWhite) "白名单" else "黑名单"}项成功", Toast.LENGTH_SHORT).show()
                    loadBlackWhiteListData() // 重新加载数据
                }
            } catch (e: Exception) {
                handler.post {
                    Toast.makeText(this@BlackWhiteListManagementActivity,
                        "添加失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    AVLEngine.Logger.error("Failed to add black white item: ${e.message}")
                }
            }
        }
    }

    private fun showDeleteConfirmDialog(item: BlackWhiteListItem) {
        MaterialAlertDialogBuilder(this)
            .setTitle("删除确认")
            .setMessage("确定要删除这个${if (item.isWhite) "白名单" else "黑名单"}项吗？\n\nHash: ${item.hash}")
            .setPositiveButton("删除") { _, _ ->
                deleteBlackWhiteItem(item)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun deleteBlackWhiteItem(item: BlackWhiteListItem) {
        val executor = Executors.newSingleThreadExecutor()
        val handler = Handler(Looper.getMainLooper())

        executor.execute {
            try {
                // 从数据库删除
                DataManager.getInstance().deleteBlackWhiteItem(item.hash)

                // 更新配置
                val currentData = DataManager.getInstance().blackWhiteListData
                AVLEngine.getInstance().updateConfig { config ->
                    config?.updateBlackWhiteList(currentData)
                }

                handler.post {
                    Toast.makeText(this@BlackWhiteListManagementActivity,
                        "删除${if (item.isWhite) "白名单" else "黑名单"}项成功", Toast.LENGTH_SHORT).show()
                    loadBlackWhiteListData() // 重新加载数据
                }
            } catch (e: Exception) {
                handler.post {
                    Toast.makeText(this@BlackWhiteListManagementActivity,
                        "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    AVLEngine.Logger.error("Failed to delete black white item: ${e.message}")
                }
            }
        }
    }

    private fun showItemDetailDialog(item: BlackWhiteListItem) {
        // 复制Hash值到剪贴板
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Hash值", item.hash)
        clipboard.setPrimaryClip(clip)

        // 显示详情对话框
        MaterialAlertDialogBuilder(this)
            .setTitle("${if (item.isWhite) "白名单" else "黑名单"}详情")
            .setMessage("Hash值: ${item.hash}\n类型: ${if (item.isWhite) "白名单" else "黑名单"}\n\n✓ Hash值已复制到剪贴板")
            .setPositiveButton("确定", null)
            .show()

        // 显示复制成功提示
        Toast.makeText(this, "Hash值已复制到剪贴板", Toast.LENGTH_SHORT).show()
    }
}
