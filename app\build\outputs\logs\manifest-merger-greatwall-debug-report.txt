-- Merging decision tree log ---
manifest
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
MERGED from [androidx.databinding:viewbinding:7.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\4e5c5143f1afa7e5c25a77dd1e6bec9b\transformed\viewbinding-7.0.4\AndroidManifest.xml:17:1-22:12
MERGED from [:avl_sdk] D:\Projects\Android\cccj-sdk\avl_sdk\build\intermediates\merged_manifest\greatwallDebug\AndroidManifest.xml:2:1-7:12
MERGED from [:avl_pc] D:\Projects\Android\cccj-sdk\avl_pc\build\intermediates\merged_manifest\greatwallDebug\AndroidManifest.xml:2:1-7:12
MERGED from [:avl_monitor] D:\Projects\Android\cccj-sdk\avl_monitor\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4caf3a473509460810eb52cbbc9013c\transformed\core-ktx-1.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\64c853aad3552efa088c612e18db7a94\transformed\material-1.6.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b4638b1c9ed59cdaafd6530664872a18\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\93d805fc7802e8c6e3e72aa454d07829\transformed\appcompat-1.4.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f54f34b9d8f16be58cd4dc430b689f50\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\a3d5d248c5b4d31bb100dfc3c3f37fb5\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\fea5c8f97bb3a1c12fc037ab085158bb\transformed\activity-1.2.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\fa7af66d03e4ff1263aa05abe5ada00d\transformed\appcompat-resources-1.4.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d2e30541c8bf03a5fca9d613fca1227\transformed\emoji2-views-helper-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\960cabab95f43eabf6dc6d1e137da9f7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e19b360d9a669301e449c814ce8cfa3e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e0bc8bf3e3db64b0ae2d7a71b18e17\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52dd5992036c60a9aa42b31cbd307c8b\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc8c0ade80f3ffc7bdcd215e094c9c51\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b623255e8bd2b32e5facc40fae995ab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\10876aee644e2bd64f0f1d504ea1787a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a4bcea613561aa0cb574f4ce7b3e0c9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfce7622f264cd6a9dd5a4d9c2300a24\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc699967d4adba86469574a813860608\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56502e952526f7d36f377a17820f6080\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1603cc54447b33400529d3919c6310\transformed\core-1.7.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\33e89e45f2b3a6b8fa5b45888ebdbf07\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7902e50f31dcd10f6a4208c62df632a\transformed\lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e917b2946af310e95f2a21aab364e5c\transformed\savedstate-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\9726c2a44db34a70f7050c90be99f2e2\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af5c729d4b33d8e7496c27b58ce12493\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\74c0a283ed29fdcaf3223e397aebfeeb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99aa386245cf1c0f19029e54f050eb7\transformed\startup-runtime-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\031e9382c7bff9cd905b5a565a13f739\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\15541061210e2751066315b3a6ac159c\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b709b2899fde4977bbb92d777ca1714b\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a630424435832d93bdd462787b0deffd\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\575ddd23d833d3516bf684078174f72b\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f36c606312637abe6bbab4ff639999e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\107707a989493ae7a70933550d4a14f6\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f05e7bb49548f4461ec7f60297e252e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e129795e2373aec5c47349884100c91e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\485b6c2d651569a89627f6e46fd5c900\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e39826c934a6128c37a8a8638ab3d68\transformed\rxandroid-3.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.jaredrummler:apk-parser:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\c4e626ad5f6b7abc07c2b3848153ca0f\transformed\apk-parser-1.0.2\AndroidManifest.xml:29:1-36:12
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
	package
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:1-87:12
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:5:5-80
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:5:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:6:5-81
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.INTERNET
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:7:5-68
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:7:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:9:5-82
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:9:22-79
application
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:11:5-85:19
MERGED from [com.google.android.material:material:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\64c853aad3552efa088c612e18db7a94\transformed\material-1.6.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\64c853aad3552efa088c612e18db7a94\transformed\material-1.6.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b4638b1c9ed59cdaafd6530664872a18\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b4638b1c9ed59cdaafd6530664872a18\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1603cc54447b33400529d3919c6310\transformed\core-1.7.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1603cc54447b33400529d3919c6310\transformed\core-1.7.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\74c0a283ed29fdcaf3223e397aebfeeb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\74c0a283ed29fdcaf3223e397aebfeeb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99aa386245cf1c0f19029e54f050eb7\transformed\startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99aa386245cf1c0f19029e54f050eb7\transformed\startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1603cc54447b33400529d3919c6310\transformed\core-1.7.0\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:19:9-35
	android:label
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:17:9-41
	android:fullBackupContent
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:15:9-54
	android:roundIcon
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:18:9-54
	tools:targetApi
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:22:9-29
	android:icon
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:16:9-43
	android:allowBackup
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:13:9-35
	android:theme
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:20:9-47
	android:dataExtractionRules
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:14:9-65
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:23:9-37
	android:usesCleartextTraffic
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:21:9-44
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:12:9-28
activity#.activity.HomeActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:24:9-44:20
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:26:13-36
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:27:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:25:13-50
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:28:13-32:29
action#android.intent.action.MAIN
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:31:17-77
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:31:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.DEFAULT+data:scheme:content
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:34:13-38:29
action#android.intent.action.VIEW
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:35:17-69
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:35:25-66
category#android.intent.category.DEFAULT
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:36:17-76
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:36:27-73
data
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:37:17-50
	android:scheme
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:37:23-47
	android:mimeType
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:42:23-45
intent-filter#action:name:android.intent.action.SEND+category:name:android.intent.category.DEFAULT+data:mimeType:*/*
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:39:13-43:29
action#android.intent.action.SEND
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:40:25-66
activity#.activity.UuidActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:45:9-47:44
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:47:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:46:13-50
activity#.activity.ScanningActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:48:9-51:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:50:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:51:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:49:13-54
activity#.activity.AntivirusSettingsActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:52:9-55:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:54:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:55:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:53:13-63
activity#.activity.CloudScanSettingsActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:56:9-59:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:58:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:59:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:57:13-63
activity#.activity.PerformanceSettingsActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:60:9-63:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:62:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:63:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:61:13-65
activity#.activity.HelpFeedbackActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:64:9-67:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:66:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:67:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:65:13-58
activity#.activity.MainActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:68:9-71:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:70:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:71:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:69:13-50
activity#.activity.ScanResultActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:72:9-75:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:74:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:75:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:73:13-56
activity#.activity.AboutActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:76:9-80:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:78:13-37
	android:theme
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:79:13-47
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:80:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:77:13-51
activity#.activity.BlackWhiteListManagementActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:81:9-84:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:83:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:84:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:82:13-70
uses-sdk
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\4e5c5143f1afa7e5c25a77dd1e6bec9b\transformed\viewbinding-7.0.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.databinding:viewbinding:7.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\4e5c5143f1afa7e5c25a77dd1e6bec9b\transformed\viewbinding-7.0.4\AndroidManifest.xml:20:5-44
MERGED from [:avl_sdk] D:\Projects\Android\cccj-sdk\avl_sdk\build\intermediates\merged_manifest\greatwallDebug\AndroidManifest.xml:5:5-44
MERGED from [:avl_sdk] D:\Projects\Android\cccj-sdk\avl_sdk\build\intermediates\merged_manifest\greatwallDebug\AndroidManifest.xml:5:5-44
MERGED from [:avl_pc] D:\Projects\Android\cccj-sdk\avl_pc\build\intermediates\merged_manifest\greatwallDebug\AndroidManifest.xml:5:5-44
MERGED from [:avl_pc] D:\Projects\Android\cccj-sdk\avl_pc\build\intermediates\merged_manifest\greatwallDebug\AndroidManifest.xml:5:5-44
MERGED from [:avl_monitor] D:\Projects\Android\cccj-sdk\avl_monitor\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:avl_monitor] D:\Projects\Android\cccj-sdk\avl_monitor\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4caf3a473509460810eb52cbbc9013c\transformed\core-ktx-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4caf3a473509460810eb52cbbc9013c\transformed\core-ktx-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.material:material:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\64c853aad3552efa088c612e18db7a94\transformed\material-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\64c853aad3552efa088c612e18db7a94\transformed\material-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b4638b1c9ed59cdaafd6530664872a18\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b4638b1c9ed59cdaafd6530664872a18\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\93d805fc7802e8c6e3e72aa454d07829\transformed\appcompat-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\93d805fc7802e8c6e3e72aa454d07829\transformed\appcompat-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f54f34b9d8f16be58cd4dc430b689f50\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f54f34b9d8f16be58cd4dc430b689f50\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\a3d5d248c5b4d31bb100dfc3c3f37fb5\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\a3d5d248c5b4d31bb100dfc3c3f37fb5\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\fea5c8f97bb3a1c12fc037ab085158bb\transformed\activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\fea5c8f97bb3a1c12fc037ab085158bb\transformed\activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\fa7af66d03e4ff1263aa05abe5ada00d\transformed\appcompat-resources-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\fa7af66d03e4ff1263aa05abe5ada00d\transformed\appcompat-resources-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d2e30541c8bf03a5fca9d613fca1227\transformed\emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d2e30541c8bf03a5fca9d613fca1227\transformed\emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\960cabab95f43eabf6dc6d1e137da9f7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\960cabab95f43eabf6dc6d1e137da9f7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e19b360d9a669301e449c814ce8cfa3e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e19b360d9a669301e449c814ce8cfa3e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e0bc8bf3e3db64b0ae2d7a71b18e17\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e0bc8bf3e3db64b0ae2d7a71b18e17\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52dd5992036c60a9aa42b31cbd307c8b\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52dd5992036c60a9aa42b31cbd307c8b\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc8c0ade80f3ffc7bdcd215e094c9c51\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc8c0ade80f3ffc7bdcd215e094c9c51\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b623255e8bd2b32e5facc40fae995ab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b623255e8bd2b32e5facc40fae995ab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\10876aee644e2bd64f0f1d504ea1787a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\10876aee644e2bd64f0f1d504ea1787a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a4bcea613561aa0cb574f4ce7b3e0c9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a4bcea613561aa0cb574f4ce7b3e0c9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfce7622f264cd6a9dd5a4d9c2300a24\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfce7622f264cd6a9dd5a4d9c2300a24\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc699967d4adba86469574a813860608\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc699967d4adba86469574a813860608\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56502e952526f7d36f377a17820f6080\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56502e952526f7d36f377a17820f6080\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1603cc54447b33400529d3919c6310\transformed\core-1.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1603cc54447b33400529d3919c6310\transformed\core-1.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\33e89e45f2b3a6b8fa5b45888ebdbf07\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\33e89e45f2b3a6b8fa5b45888ebdbf07\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7902e50f31dcd10f6a4208c62df632a\transformed\lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7902e50f31dcd10f6a4208c62df632a\transformed\lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e917b2946af310e95f2a21aab364e5c\transformed\savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e917b2946af310e95f2a21aab364e5c\transformed\savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\9726c2a44db34a70f7050c90be99f2e2\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\9726c2a44db34a70f7050c90be99f2e2\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af5c729d4b33d8e7496c27b58ce12493\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af5c729d4b33d8e7496c27b58ce12493\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\74c0a283ed29fdcaf3223e397aebfeeb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\74c0a283ed29fdcaf3223e397aebfeeb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99aa386245cf1c0f19029e54f050eb7\transformed\startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99aa386245cf1c0f19029e54f050eb7\transformed\startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\031e9382c7bff9cd905b5a565a13f739\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\031e9382c7bff9cd905b5a565a13f739\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\15541061210e2751066315b3a6ac159c\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\15541061210e2751066315b3a6ac159c\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b709b2899fde4977bbb92d777ca1714b\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b709b2899fde4977bbb92d777ca1714b\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a630424435832d93bdd462787b0deffd\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a630424435832d93bdd462787b0deffd\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\575ddd23d833d3516bf684078174f72b\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\575ddd23d833d3516bf684078174f72b\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f36c606312637abe6bbab4ff639999e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f36c606312637abe6bbab4ff639999e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\107707a989493ae7a70933550d4a14f6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\107707a989493ae7a70933550d4a14f6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f05e7bb49548f4461ec7f60297e252e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f05e7bb49548f4461ec7f60297e252e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e129795e2373aec5c47349884100c91e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e129795e2373aec5c47349884100c91e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\485b6c2d651569a89627f6e46fd5c900\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\485b6c2d651569a89627f6e46fd5c900\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e39826c934a6128c37a8a8638ab3d68\transformed\rxandroid-3.0.0\AndroidManifest.xml:18:5-43
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e39826c934a6128c37a8a8638ab3d68\transformed\rxandroid-3.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.jaredrummler:apk-parser:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\c4e626ad5f6b7abc07c2b3848153ca0f\transformed\apk-parser-1.0.2\AndroidManifest.xml:32:5-34:41
MERGED from [com.jaredrummler:apk-parser:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\c4e626ad5f6b7abc07c2b3848153ca0f\transformed\apk-parser-1.0.2\AndroidManifest.xml:32:5-34:41
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
		INJECTED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml
activity#com.antiy.demo.activity.HomeActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:24:9-44:20
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:26:13-36
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:27:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:25:13-50
activity#com.antiy.demo.activity.UuidActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:45:9-47:44
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:47:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:46:13-50
activity#com.antiy.demo.activity.ScanningActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:48:9-51:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:50:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:51:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:49:13-54
activity#com.antiy.demo.activity.AntivirusSettingsActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:52:9-55:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:54:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:55:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:53:13-63
activity#com.antiy.demo.activity.CloudScanSettingsActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:56:9-59:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:58:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:59:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:57:13-63
activity#com.antiy.demo.activity.PerformanceSettingsActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:60:9-63:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:62:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:63:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:61:13-65
activity#com.antiy.demo.activity.HelpFeedbackActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:64:9-67:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:66:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:67:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:65:13-58
activity#com.antiy.demo.activity.MainActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:68:9-71:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:70:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:71:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:69:13-50
activity#com.antiy.demo.activity.ScanResultActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:72:9-75:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:74:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:75:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:73:13-56
activity#com.antiy.demo.activity.AboutActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:76:9-80:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:78:13-37
	android:theme
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:79:13-47
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:80:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:77:13-51
activity#com.antiy.demo.activity.BlackWhiteListManagementActivity
ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:81:9-84:44
	android:exported
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:83:13-37
	tools:replace
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:84:13-41
	android:name
		ADDED from D:\Projects\Android\cccj-sdk\app\src\main\AndroidManifest.xml:82:13-70
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99aa386245cf1c0f19029e54f050eb7\transformed\startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99aa386245cf1c0f19029e54f050eb7\transformed\startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:30:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:28:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:29:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:27:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc0ed9ede9a90185243f6dac9b518756\transformed\emoji2-1.0.0\AndroidManifest.xml:32:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2134913697e06a8f549755ef19cf3e\transformed\lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
