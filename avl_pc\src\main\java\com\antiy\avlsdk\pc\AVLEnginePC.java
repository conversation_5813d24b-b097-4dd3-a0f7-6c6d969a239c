package com.antiy.avlsdk.pc;

import static com.antiy.avlsdk.pc.PCConstant.VENDOR_DIR;

import android.content.Context;
import android.util.Log;
import android.util.Pair;

import java.io.File;
import java.io.IOException;

/**
 * VirtualApp Native Project
 */
public class AVLEnginePC {

    private static final String TAG = AVLEnginePC.class.getSimpleName();
    private final String TARGET_DIR = "avlsdk_pc";
    private final String ZIP_SUFFIX = ".zip";
    private final String VIDS_DIR = "/data/vids";

    private static volatile AVLEnginePC mInstance;
    private static String mAVLPath;

    public String getAVLPCPath() {
        return mAVLPath;
    }

    static {
        try {
            System.loadLibrary("avlsdk_pc");
        } catch (Throwable e) {
            Log.d(TAG, e.getMessage());
        }
    }

    private AVLEnginePC() {
    }

    public static AVLEnginePC getInstance() {
        if (mInstance == null) {
            synchronized (AVLEnginePC.class) {
                if (mInstance == null) {
                    mInstance = new AVLEnginePC();
                }
            }
        }

        return mInstance;
    }

    public void prepareData(Context context) {
        // 只有长城版本才需要准备PC引擎数据
        File avlsdkPc = new File(VIDS_DIR + File.separator + TARGET_DIR);
        mAVLPath = avlsdkPc.getAbsolutePath();

        Log.e("AVLEnginePC", "avlsdkPc exist: " + avlsdkPc.exists());
        Log.e("AVLEnginePC", "avlsdkPc HasFiles: " + AssetsUtil.isFolderHasFiles(mAVLPath));
        Log.e("AVLEnginePC", "FLAVOR " + BuildConfig.FLAVOR);
        if (avlsdkPc.exists() && AssetsUtil.isFolderHasFiles(mAVLPath)) {
            Log.e("AVLEnginePC", "It has been copied");
            return;
        }
        Log.e("AVLEnginePC", "Not copied, about to be copied");
        try {
            if (BuildConfig.FLAVOR.equals("greatwall")) {
                Log.e("AVLEnginePC", "greatwall zip coping");
                VendorZipUtils.unzipFromVendor(VENDOR_DIR + TARGET_DIR + ZIP_SUFFIX,VIDS_DIR);
            } else {
                AssetsUtil.unzipFromAssets(context, VIDS_DIR,TARGET_DIR + ZIP_SUFFIX);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public int init() {
        return loadEngine(mAVLPath);
    }

    //ret:
    //0: success, -1: 失败（后面再完善错误码）
    public native int loadEngine(String sdk_dir);

    public native void unloadEngine();

    public native String scan(String filePath);

    public native Pair<String, byte[]> scanWithMd5(String filePath);

    public native void setScanEnabled(boolean enabled);

    public native String getDbInfo();

    public native String getCurVersion();
}
