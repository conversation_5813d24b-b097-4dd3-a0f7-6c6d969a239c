# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 43ms
  [gap of 11ms]
create_cxx_tasks completed in 54ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 63ms
create_cxx_tasks completed in 72ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARMEABI_V7A-model 10ms
    [gap of 40ms]
  create-initial-cxx-model completed in 58ms
create_cxx_tasks completed in 65ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 47ms
    create-variant-model 16ms
    create-ARM64_V8A-model 31ms
    create-X86-model 81ms
    create-X86_64-model 16ms
    create-variant-model 36ms
    create-X86-model 43ms
  create-initial-cxx-model completed in 270ms
  [gap of 15ms]
create_cxx_tasks completed in 285ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 39ms
create_cxx_tasks completed in 46ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 53ms
create_cxx_tasks completed in 60ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 23ms]
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-module-model 13ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    [gap of 10ms]
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 121ms
  [gap of 16ms]
create_cxx_tasks completed in 137ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    [gap of 18ms]
    create-ARM64_V8A-model 10ms
    [gap of 44ms]
    create-X86-model 10ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 112ms
create_cxx_tasks completed in 115ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 29ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 25ms]
    create-X86-model 14ms
    [gap of 41ms]
  create-initial-cxx-model completed in 80ms
  [gap of 14ms]
create_cxx_tasks completed in 95ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 52ms]
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 67ms
create_cxx_tasks completed in 75ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 19ms]
    create-X86-model 14ms
    [gap of 44ms]
  create-initial-cxx-model completed in 77ms
  [gap of 10ms]
create_cxx_tasks completed in 88ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 50ms
  [gap of 10ms]
create_cxx_tasks completed in 60ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 13ms
    [gap of 27ms]
    create-module-model 11ms
    create-variant-model 12ms
    [gap of 24ms]
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 112ms
  [gap of 17ms]
create_cxx_tasks completed in 129ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 57ms
create_cxx_tasks completed in 65ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 58ms
  [gap of 15ms]
create_cxx_tasks completed in 74ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 50ms
create_cxx_tasks completed in 59ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 50ms
  [gap of 10ms]
create_cxx_tasks completed in 60ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 49ms
create_cxx_tasks completed in 57ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARMEABI_V7A-model 14ms
    create-X86-model 11ms
    create-module-model 12ms
    [gap of 29ms]
  create-initial-cxx-model completed in 89ms
create_cxx_tasks completed in 99ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 36ms]
    create-module-model 11ms
    [gap of 37ms]
  create-initial-cxx-model completed in 95ms
  [gap of 20ms]
create_cxx_tasks completed in 115ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 54ms
  [gap of 12ms]
create_cxx_tasks completed in 66ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 26ms]
    create-X86-model 19ms
    [gap of 49ms]
  create-initial-cxx-model completed in 94ms
create_cxx_tasks completed in 98ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 18ms
create_cxx_tasks completed in 19ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 49ms
create_cxx_tasks completed in 59ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 50ms
  [gap of 10ms]
create_cxx_tasks completed in 61ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 37ms]
    create-module-model 12ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    [gap of 16ms]
  create-initial-cxx-model completed in 96ms
create_cxx_tasks completed in 101ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARMEABI_V7A-model 10ms
    [gap of 22ms]
    create-module-model 12ms
    [gap of 36ms]
  create-initial-cxx-model completed in 89ms
  [gap of 11ms]
create_cxx_tasks completed in 101ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 25ms]
    create-X86-model 11ms
    create-module-model 15ms
    [gap of 35ms]
  create-initial-cxx-model completed in 105ms
  [gap of 17ms]
create_cxx_tasks completed in 122ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 46ms
create_cxx_tasks completed in 56ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 60ms
create_cxx_tasks completed in 67ms

