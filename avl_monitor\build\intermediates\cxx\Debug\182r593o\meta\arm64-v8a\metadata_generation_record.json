[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: arm64-v8a", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2034456155}, {"level_": 0, "message_": "rebuilding JSON D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a\\android_gradle_build.json due to:", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -201919607}, {"level_": 0, "message_": "- a dependent build file changed", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1960003538}, {"level_": 0, "message_": "  - D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1690043105}, {"level_": 0, "message_": "keeping json folder 'D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a' but regenerating project", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1678654859}, {"level_": 0, "message_": "executing cmake Executable : D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\cmake.exe\narguments : \n-HD:\\Projects\\Android\\cccj-sdk\\avl_monitor\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=21\n-DANDROID_PLATFORM=android-21\n-DANDROID_ABI=arm64-v8a\n-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\n-DANDROID_NDK=D:\\Android\\Sdk\\ndk\\27.0.12077973\n-DCMAKE_ANDROID_NDK=D:\\Android\\Sdk\\ndk\\27.0.12077973\n-DCMAKE_TOOLCHAIN_FILE=D:\\Android\\Sdk\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\ninja.exe\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj\\arm64-v8a\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj\\arm64-v8a\n-DCMAKE_BUILD_TYPE=Debug\n-BD:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a\n-GNinja\njvmArgs : \n\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1681900389}, {"level_": 0, "message_": "\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -731209145}, {"level_": 0, "message_": "[== \"CMake Server\" ==[\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1908325087}, {"level_": 0, "message_": "{\"supportedProtocolVersions\":[{\"isExperimental\":true,\"major\":1,\"minor\":1}],\"type\":\"hello\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1671257600}, {"level_": 0, "message_": "]== \"CMake Server\" ==]\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 780474081}, {"level_": 0, "message_": "{\n  \"type\": \"handshake\",\n  \"cookie\": \"gradle-cmake-cookie\",\n  \"protocolVersion\": {\n    \"isExperimental\": true,\n    \"major\": 1,\n    \"minor\": 1\n  },\n  \"sourceDirectory\": \"D:/Projects/Android/cccj-sdk/avl_monitor\",\n  \"buildDirectory\": \"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a\",\n  \"generator\": \"Ninja\"\n}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1244133929}, {"level_": 0, "message_": "{\"cookie\":\"gradle-cmake-cookie\",\"inReplyTo\":\"handshake\",\"type\":\"reply\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1506249886}, {"level_": 0, "message_": "{\n  \"type\": \"configure\",\n  \"cacheArguments\": [\n    \"\",\n    \"-DCMAKE_SYSTEM_NAME\\u003dAndroid\",\n    \"-DCMAKE_EXPORT_COMPILE_COMMANDS\\u003dON\",\n    \"-DCMAKE_SYSTEM_VERSION\\u003d21\",\n    \"-DANDROID_PLATFORM\\u003dandroid-21\",\n    \"-DANDROID_ABI\\u003darm64-v8a\",\n    \"-DCMAKE_ANDROID_ARCH_ABI\\u003darm64-v8a\",\n    \"-DANDROID_NDK\\u003dD:\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\",\n    \"-DCMAKE_ANDROID_NDK\\u003dD:\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\",\n    \"-DCMAKE_TOOLCHAIN_FILE\\u003dD:\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\",\n    \"-DCMAKE_MAKE_PROGRAM\\u003dD:\\\\Android\\\\Sdk\\\\cmake\\\\3.10.2.4988404\\\\bin\\\\ninja.exe\",\n    \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY\\u003dD:\\\\Projects\\\\Android\\\\cccj-sdk\\\\avl_monitor\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\182r593o\\\\obj\\\\arm64-v8a\",\n    \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY\\u003dD:\\\\Projects\\\\Android\\\\cccj-sdk\\\\avl_monitor\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\182r593o\\\\obj\\\\arm64-v8a\",\n    \"-DCMAKE_BUILD_TYPE\\u003dDebug\"\n  ]\n}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 784657501}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"configure\",\"message\":\"CMake Warning at D:/Android/Sdk/ndk/27.0.12077973/build/cmake/android-legacy.toolchain.cmake:431 (message):\\n  An old version of CMake is being used that cannot automatically detect\\n  compiler attributes.  Compiler identification is being bypassed.  Some\\n  values may be wrong or missing.  Update to CMake 3.19 or newer to use\\n  CMake's built-in compiler identification.\\nCall Stack (most recent call first):\\n  D:/Android/Sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake:55 (include)\\n  .cxx/Debug/182r593o/arm64-v8a/CMakeFiles/3.10.2/CMakeSystem.cmake:6 (include)\\n  CMakeLists.txt:2 (project)\\n\\n\",\"title\":\"Warning\",\"type\":\"message\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1466966557}, {"level_": 2, "message_": "CMake Warning at D:/Android/Sdk/ndk/27.0.12077973/build/cmake/android-legacy.toolchain.cmake:431 (message):\r\n  An old version of CMake is being used that cannot automatically detect\r\n  compiler attributes.  Compiler identification is being bypassed.  Some\r\n  values may be wrong or missing.  Update to CMake 3.19 or newer to use\r\n  CMake's built-in compiler identification.\r\nCall Stack (most recent call first):\r\n  D:/Android/Sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake:55 (include)\r\n  .cxx/Debug/182r593o/arm64-v8a/CMakeFiles/3.10.2/CMakeSystem.cmake:6 (include)\r\n  CMakeLists.txt:2 (project)\r\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -320962146}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"configure\",\"message\":\"OUTPUT TO: D:\\\\Projects\\\\Android\\\\cccj-sdk\\\\avl_monitor\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\182r593o\\\\obj\\\\arm64-v8a\",\"type\":\"message\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 631770304}, {"level_": 0, "message_": "OUTPUT TO: D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj\\arm64-v8a", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1281245754}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"configure\",\"progressCurrent\":900,\"progressMaximum\":1000,\"progressMessage\":\"Configuring\",\"progressMinimum\":0,\"type\":\"progress\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1793580841}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"configure\",\"progressCurrent\":950,\"progressMaximum\":1000,\"progressMessage\":\"Configuring\",\"progressMinimum\":0,\"type\":\"progress\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1289082610}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"configure\",\"progressCurrent\":1000,\"progressMaximum\":1000,\"progressMessage\":\"Configuring\",\"progressMinimum\":0,\"type\":\"progress\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 871885877}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"configure\",\"message\":\"Configuring done\",\"type\":\"message\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 644660088}, {"level_": 0, "message_": "Configuring done", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1930705856}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"configure\",\"type\":\"reply\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1138684661}, {"level_": 0, "message_": "{\"type\":\"compute\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -173858166}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"compute\",\"progressCurrent\":500,\"progressMaximum\":1000,\"progressMessage\":\"Generating\",\"progressMinimum\":0,\"type\":\"progress\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1966016853}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"compute\",\"progressCurrent\":1000,\"progressMaximum\":1000,\"progressMessage\":\"Generating\",\"progressMinimum\":0,\"type\":\"progress\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 754728775}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"compute\",\"message\":\"Generating done\",\"type\":\"message\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1518038254}, {"level_": 0, "message_": "Generating done", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2112458437}, {"level_": 0, "message_": "{\"cookie\":\"\",\"inReplyTo\":\"compute\",\"type\":\"reply\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -2118727302}, {"level_": 0, "message_": "{\"type\":\"cmakeInputs\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -102623125}, {"level_": 0, "message_": "{\"buildFiles\":[{\"isCMake\":true,\"isTemporary\":false,\"sources\":[\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeSystemSpecificInitialize.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Initialize.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeSystemSpecificInformation.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeGenericSystem.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Linux.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/UnixPaths.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCInformation.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeLanguageInformation.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-C.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/CMakeCommonCompilerMacros.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/GNU.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/CMakeCommonCompilerMacros.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang-C.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCommonLanguageInclude.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCXXInformation.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeLanguageInformation.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-CXX.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang-CXX.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang.cmake\",\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCommonLanguageInclude.cmake\"]},{\"isCMake\":false,\"isTemporary\":false,\"sources\":[\"CMakeLists.txt\",\"D:/Android/Sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake\",\"D:/Android/Sdk/ndk/27.0.12077973/build/cmake/android-legacy.toolchain.cmake\",\"D:/Android/Sdk/ndk/27.0.12077973/build/cmake/abis.cmake\",\"D:/Android/Sdk/ndk/27.0.12077973/build/cmake/platforms.cmake\",\"D:/Android/Sdk/ndk/27.0.12077973/build/cmake/compiler_id.cmake\",\"src/main/cpp/argparse/CMakeLists.txt\"]},{\"isCMake\":false,\"isTemporary\":true,\"sources\":[\".cxx/Debug/182r593o/arm64-v8a/CMakeFiles/3.10.2/CMakeSystem.cmake\",\".cxx/Debug/182r593o/arm64-v8a/CMakeFiles/3.10.2/CMakeCCompiler.cmake\",\".cxx/Debug/182r593o/arm64-v8a/CMakeFiles/3.10.2/CMakeCXXCompiler.cmake\"]}],\"cmakeRootDirectory\":\"D:/Android/Sdk/cmake/3.10.2.4988404/share/cmake-3.10\",\"cookie\":\"\",\"inReplyTo\":\"cmakeInputs\",\"sourceDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor\",\"type\":\"reply\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 775712726}, {"level_": 0, "message_": "{\"type\":\"codemodel\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -403776703}, {"level_": 0, "message_": "{\"configurations\":[{\"name\":\"Debug\",\"projects\":[{\"buildDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a/src/main/cpp/argparse\",\"name\":\"argparse\",\"sourceDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse\",\"targets\":[{\"artifacts\":[\"D:/Projects/Android/cccj-sdk/avl_monitor/build/intermediates/cxx/Debug/182r593o/obj/arm64-v8a/libargparse.so\"],\"buildDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a/src/main/cpp/argparse\",\"fileGroups\":[{\"compileFlags\":\"-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC  \",\"defines\":[\"argparse_shared_EXPORTS\"],\"includePath\":[{\"path\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse/.\"}],\"isGenerated\":false,\"language\":\"C\",\"sources\":[\"argparse.c\"]}],\"fullName\":\"libargparse.so\",\"linkFlags\":\"-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments\",\"linkLibraries\":\"-latomic -lm\",\"linkerLanguage\":\"C\",\"name\":\"argparse_shared\",\"sourceDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse\",\"sysroot\":\"D:/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot\",\"type\":\"SHARED_LIBRARY\"},{\"artifacts\":[\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a/src/main/cpp/argparse/libargparse_static.a\"],\"buildDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a/src/main/cpp/argparse\",\"fileGroups\":[{\"compileFlags\":\"-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC  \",\"includePath\":[{\"path\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse/.\"}],\"isGenerated\":false,\"language\":\"C\",\"sources\":[\"argparse.c\"]}],\"fullName\":\"libargparse_static.a\",\"linkerLanguage\":\"C\",\"name\":\"argparse_static\",\"sourceDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse\",\"sysroot\":\"D:/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot\",\"type\":\"STATIC_LIBRARY\"}]},{\"buildDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a\",\"name\":\"cpu_usage\",\"sourceDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor\",\"targets\":[{\"artifacts\":[\"D:/Projects/Android/cccj-sdk/avl_monitor/build/intermediates/cxx/Debug/182r593o/obj/arm64-v8a/libargparse.so\"],\"buildDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a/src/main/cpp/argparse\",\"fileGroups\":[{\"compileFlags\":\"-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC  \",\"defines\":[\"argparse_shared_EXPORTS\"],\"includePath\":[{\"path\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse/.\"}],\"isGenerated\":false,\"language\":\"C\",\"sources\":[\"argparse.c\"]}],\"fullName\":\"libargparse.so\",\"linkFlags\":\"-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments\",\"linkLibraries\":\"-latomic -lm\",\"linkerLanguage\":\"C\",\"name\":\"argparse_shared\",\"sourceDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse\",\"sysroot\":\"D:/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot\",\"type\":\"SHARED_LIBRARY\"},{\"artifacts\":[\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a/src/main/cpp/argparse/libargparse_static.a\"],\"buildDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a/src/main/cpp/argparse\",\"fileGroups\":[{\"compileFlags\":\"-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC  \",\"includePath\":[{\"path\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse/.\"}],\"isGenerated\":false,\"language\":\"C\",\"sources\":[\"argparse.c\"]}],\"fullName\":\"libargparse_static.a\",\"linkerLanguage\":\"C\",\"name\":\"argparse_static\",\"sourceDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse\",\"sysroot\":\"D:/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot\",\"type\":\"STATIC_LIBRARY\"},{\"artifacts\":[\"D:/Projects/Android/cccj-sdk/avl_monitor/build/intermediates/cxx/Debug/182r593o/obj/arm64-v8a/cpu_usage\"],\"buildDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor/.cxx/Debug/182r593o/arm64-v8a\",\"fileGroups\":[{\"compileFlags\":\"-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIE  \",\"includePath\":[{\"path\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse\"},{\"path\":\"D:/Projects/Android/cccj-sdk/avl_monitor/src/main/cpp/argparse/.\"}],\"isGenerated\":false,\"language\":\"C\",\"sources\":[\"src/main/cpp/main.c\"]}],\"fullName\":\"cpu_usage\",\"linkFlags\":\"-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments\",\"linkLanguageFlags\":\"-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info\",\"linkLibraries\":\"-llog src\\\\main\\\\cpp\\\\argparse\\\\libargparse_static.a -latomic -lm\",\"linkerLanguage\":\"C\",\"name\":\"cpu_usage\",\"sourceDirectory\":\"D:/Projects/Android/cccj-sdk/avl_monitor\",\"sysroot\":\"D:/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot\",\"type\":\"EXECUTABLE\"}]}]}],\"cookie\":\"\",\"inReplyTo\":\"codemodel\",\"type\":\"reply\"}\n", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1309880792}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1199089150}, {"level_": 0, "message_": "write command file D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a\\metadata_generation_command.txt", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1539924499}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1157805062}]