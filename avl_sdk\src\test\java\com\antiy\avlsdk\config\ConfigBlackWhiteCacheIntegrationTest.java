package com.antiy.avlsdk.config;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.content.Context;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.CacheEntity;
import com.antiy.avlsdk.storage.DataManager;
import com.antiy.avlsdk.storage.DatabaseHelper;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import java.util.HashMap;

/**
 * 集成测试：验证Config.updateBlackWhiteList方法能够正确更新缓存
 * 测试从Config层到DataManager层的完整调用链
 */
@RunWith(RobolectricTestRunner.class)
public class ConfigBlackWhiteCacheIntegrationTest {

    @Mock
    private DatabaseHelper mockDbHelper;
    
    @Mock
    private AVLEngine mockEngine;
    
    private Config config;
    private DataManager dataManager;
    private Context context;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        context = RuntimeEnvironment.getApplication();
        
        // Mock AVLEngine
        try (MockedStatic<AVLEngine> mockedEngine = mockStatic(AVLEngine.class)) {
            mockedEngine.when(AVLEngine::getInstance).thenReturn(mockEngine);
            when(mockEngine.getContext()).thenReturn(context);
            
            dataManager = DataManager.getInstance();
            config = new Config();
        }
        
        // 使用反射设置mock的DatabaseHelper
        try {
            java.lang.reflect.Field dbHelperField = DataManager.class.getDeclaredField("dbHelper");
            dbHelperField.setAccessible(true);
            dbHelperField.set(dataManager, mockDbHelper);
        } catch (Exception e) {
            fail("Failed to set mock DatabaseHelper: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateBlackWhiteList_UpdatesCache() {
        // 准备测试数据
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put("hash1", true);  // 白名单
        blackWhiteMap.put("hash2", false); // 黑名单
        
        long timestamp1 = System.currentTimeMillis();
        long timestamp2 = System.currentTimeMillis() + 1000;
        
        // Mock缓存中已存在这些hash的数据
        when(mockDbHelper.getCacheData("hash1")).thenReturn(new CacheEntity("hash1", "原有病毒1", timestamp1));
        when(mockDbHelper.getCacheData("hash2")).thenReturn(new CacheEntity("hash2", "", timestamp2));
        
        // 执行测试：调用Config的updateBlackWhiteList方法
        config.updateBlackWhiteList(blackWhiteMap);
        
        // 验证：应该调用保存黑白名单
        verify(mockDbHelper).insertBlackWhiteData("hash1", "true");
        verify(mockDbHelper).insertBlackWhiteData("hash2", "false");
        
        // 验证：应该更新缓存数据
        verify(mockDbHelper).insertCacheData("hash1", "", timestamp1);      // 白名单：virusName为空
        verify(mockDbHelper).insertCacheData("hash2", "黑名单病毒", timestamp2); // 黑名单：virusName为"黑名单病毒"
    }

    @Test
    public void testUpdateBlackWhiteList_OnlyUpdatesExistingCache() {
        // 准备测试数据
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put("existing_hash", true);
        blackWhiteMap.put("non_existing_hash", false);
        
        long timestamp = System.currentTimeMillis();
        
        // Mock：一个hash在缓存中存在，另一个不存在
        when(mockDbHelper.getCacheData("existing_hash")).thenReturn(new CacheEntity("existing_hash", "病毒", timestamp));
        when(mockDbHelper.getCacheData("non_existing_hash")).thenReturn(null);
        
        // 执行测试
        config.updateBlackWhiteList(blackWhiteMap);
        
        // 验证：只有存在缓存的hash才会被更新
        verify(mockDbHelper).insertCacheData("existing_hash", "", timestamp);
        verify(mockDbHelper, never()).insertCacheData(eq("non_existing_hash"), anyString(), anyLong());
    }

    @Test
    public void testUpdateBlackWhiteList_NullMap() {
        // 执行测试：传入null
        config.updateBlackWhiteList(null);
        
        // 验证：不应该有任何数据库操作
        verify(mockDbHelper, never()).insertBlackWhiteData(anyString(), anyString());
        verify(mockDbHelper, never()).getCacheData(anyString());
        verify(mockDbHelper, never()).insertCacheData(anyString(), anyString(), anyLong());
    }

    @Test
    public void testUpdateBlackWhiteList_EmptyMap() {
        // 执行测试：传入空map
        config.updateBlackWhiteList(new HashMap<>());
        
        // 验证：不应该有任何数据库操作
        verify(mockDbHelper, never()).insertBlackWhiteData(anyString(), anyString());
        verify(mockDbHelper, never()).getCacheData(anyString());
        verify(mockDbHelper, never()).insertCacheData(anyString(), anyString(), anyLong());
    }

    @Test
    public void testUpdateBlackWhiteList_CacheConflictScenario() {
        // 模拟用户报告的场景：
        // 1. 缓存中有一个文件被标记为黑名单（有病毒名）
        // 2. 用户将该文件设置为白名单
        // 3. 验证缓存被正确更新为白名单（virusName为空）
        
        String conflictHash = "conflict_file_hash";
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put(conflictHash, true); // 用户设置为白名单
        
        long cacheTimestamp = System.currentTimeMillis() - 3600000; // 1小时前的缓存
        
        // Mock：缓存中该文件被标记为恶意（有病毒名）
        CacheEntity maliciousCache = new CacheEntity(conflictHash, "Trojan.Generic", cacheTimestamp);
        when(mockDbHelper.getCacheData(conflictHash)).thenReturn(maliciousCache);
        
        // 执行测试：用户更新黑白名单，将该文件设为白名单
        config.updateBlackWhiteList(blackWhiteMap);
        
        // 验证：黑白名单被保存
        verify(mockDbHelper).insertBlackWhiteData(conflictHash, "true");
        
        // 验证：缓存被更新为白名单（virusName为空），时间戳保持不变
        verify(mockDbHelper).insertCacheData(conflictHash, "", cacheTimestamp);
        
        // 这样确保了下次扫描时，即使缓存优先级高于黑白名单，
        // 缓存中的结果也与用户的黑白名单设置一致
    }

    @Test
    public void testUpdateBlackWhiteList_WhiteToBlackScenario() {
        // 测试相反场景：从白名单改为黑名单
        
        String testHash = "white_to_black_hash";
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put(testHash, false); // 设置为黑名单
        
        long cacheTimestamp = System.currentTimeMillis() - 1800000; // 30分钟前的缓存
        
        // Mock：缓存中该文件被标记为安全（无病毒名）
        CacheEntity safeCache = new CacheEntity(testHash, "", cacheTimestamp);
        when(mockDbHelper.getCacheData(testHash)).thenReturn(safeCache);
        
        // 执行测试：用户更新黑白名单，将该文件设为黑名单
        config.updateBlackWhiteList(blackWhiteMap);
        
        // 验证：黑白名单被保存
        verify(mockDbHelper).insertBlackWhiteData(testHash, "false");
        
        // 验证：缓存被更新为黑名单（virusName为"黑名单病毒"），时间戳保持不变
        verify(mockDbHelper).insertCacheData(testHash, "黑名单病毒", cacheTimestamp);
    }
}
