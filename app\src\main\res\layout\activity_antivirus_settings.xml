<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_gray"
    android:fitsSystemWindows="true">

    <!-- 顶部标题栏 -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:fitsSystemWindows="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:contentInsetStartWithNavigation="0dp"
            app:layout_scrollFlags="scroll|enterAlways">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_back"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:text="杀毒引擎高级设置"
                android:textColor="@color/black"
                android:textSize="18sp" />

        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- 内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 扫描路径配置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="扫描路径配置"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <RelativeLayout
                        android:id="@+id/layoutCustomPath"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="自定义扫描路径"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvSelectedPath"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/ivArrow"
                            android:text="点击选择扫描路径"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <ImageView
                            android:id="@+id/ivArrow"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_alignParentEnd="true"
                            android:src="@drawable/ic_arrow_right" />
                    </RelativeLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 黑白名单配置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="黑白名单配置"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启用白名单"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/switchWhitelist"
                            android:text="跳过扫描受信任的文件和应用"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchWhitelist"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启用黑名单"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/switchBlacklist"
                            android:text="强制检查特定文件和应用"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchBlacklist"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true" />
                    </RelativeLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManageWhitelist"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="start|center_vertical"
                        android:text="管理白名单"
                        android:textColor="@color/black"
                        app:icon="@drawable/ic_arrow_right"
                        app:iconGravity="end" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManageBlacklist"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start|center_vertical"
                        android:text="管理黑名单"
                        android:textColor="@color/black"
                        app:icon="@drawable/ic_arrow_right"
                        app:iconGravity="end" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 缓存配置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="缓存配置"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="缓存有效期"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/spinnerCacheValidity"
                            android:text="设置缓存结果的保留时间"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <Spinner
                            android:id="@+id/spinnerCacheValidity"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/layoutCacheSize"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="缓存条目数限制"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvCacheSize"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/btnSetCache"
                            android:text="当前缓存: 0 条 / 10000 条"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <Button
                            android:id="@+id/btnSetCache"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:text="设置" />

                        <Button
                            android:id="@+id/btnClearCache"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_toStartOf="@id/btnSetCache"
                            android:layout_marginEnd="8dp"
                            android:text="清除" />
                    </RelativeLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout> 