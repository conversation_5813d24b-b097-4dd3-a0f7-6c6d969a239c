<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.antiy.demo"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <application
        android:name="com.antiy.demo.App"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AntiVirus"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.antiy.demo.activity.HomeActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="content" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="*/*" />
            </intent-filter>
        </activity>
        <activity android:name="com.antiy.demo.activity.UuidActivity" />
        <activity
            android:name="com.antiy.demo.activity.ScanningActivity"
            android:exported="false" />
        <activity
            android:name="com.antiy.demo.activity.AntivirusSettingsActivity"
            android:exported="false" />
        <activity
            android:name="com.antiy.demo.activity.CloudScanSettingsActivity"
            android:exported="false" />
        <activity
            android:name="com.antiy.demo.activity.PerformanceSettingsActivity"
            android:exported="false" />
        <activity
            android:name="com.antiy.demo.activity.HelpFeedbackActivity"
            android:exported="false" />
        <activity
            android:name="com.antiy.demo.activity.MainActivity"
            android:exported="false" />
        <activity
            android:name="com.antiy.demo.activity.ScanResultActivity"
            android:exported="false" />
        <activity
            android:name="com.antiy.demo.activity.AboutActivity"
            android:exported="false"
            android:theme="@style/Theme.About" />
        <activity
            android:name="com.antiy.demo.activity.BlackWhiteListManagementActivity"
            android:exported="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.antiy.demo.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>