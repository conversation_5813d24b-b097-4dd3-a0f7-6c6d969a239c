apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'com.kezong.fat-aar'
apply plugin: 'org.jetbrains.dokka'

def currentTime = new Date().format("yyyyMMddHHmmss", TimeZone.getTimeZone('GMT+8'))
// 添加输出配置
def outputConfig = [
        outputDir: new File(project.projectDir, "../tests"),
        getOutputFileName: { variant ->
            "avlsdk_${android.defaultConfig.versionName}_${variant.flavorName}_${variant.buildType.name}.aar"
        }
]

android {
    namespace 'com.antiy.avlsdk'
    compileSdk 34

    defaultConfig {
        minSdk 26
        versionName "1.1" + "_" + currentTime
        buildConfigField "String", "SDK_VERSION", "\"${versionName}\""
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
        ndk {
            abiFilters 'arm64-v8a'
        }
    }

    // 定义产品变体
    flavorDimensions "client"
    productFlavors {
        // 全量版本
        base {
            dimension "client"
        }
        // 长城版本
        greatwall {
            dimension "client"
        }
        // 长安版本
        changan {
            dimension "client"
        }
    }

    buildTypes {
        all {
            externalNativeBuild {
                cmake {
                    cFlags "-DAVLM_USE_AUTH"
                    cppFlags "-DAVLM_USE_AUTH"
                }
            }
        }

        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }

    lintOptions {
        abortOnError false
    }

    // 配置输出
    libraryVariants.all { variant ->
        variant.outputs.all { output ->
            outputFileName = outputConfig.getOutputFileName(variant)
        }
    }

    // 配置 sourceSets
    sourceSets {
        base {
            assets.srcDirs = ['src/main/assets']
        }
        greatwall {
            assets.srcDirs = ['src/greatwall/assets']
        }
        changan {
            assets.srcDirs = ['src/changan/assets']
        }
    }

    packagingOptions {
        exclude "lib/armeabi/lib*.so"
        exclude "lib/armeabi-v7a/lib*.so"
        exclude "lib/x86/lib*.so"
        exclude "lib/x86_64/lib*.so"
    }

}

// 简化 copyAarToTestDir 任务
tasks.register('copyAarToTestDir') {
    doLast {
        println "开始复制 AAR 文件到目标目录..."
        fileTree(dir: "${project.buildDir}/outputs/aar", include: "*.aar").forEach { file ->
            new File(new File(project.projectDir, "../tests"), file.name).bytes = file.bytes
        }
        println "已复制 AAR 文件到 ${project.projectDir}/../tests"
    }
}
// 构建长城版本 AAR
tasks.register('buildGreatwallReleaseAar') {
    dependsOn cleanOutputsAar
    dependsOn 'assembleGreatwallRelease'
    finalizedBy copyAarToTestDir

    group = 'AVL SDK'
    description = '构建长城版本 Release AAR'

    doLast {
        println """
                    ===================================
                    长城版本 Release AAR 构建完成！
                    输出目录: ${project.buildDir}/outputs/aar
                    复制目录: ${project.projectDir}/../tests
                    ===================================
                """
    }
}

// 构建长安版本 AAR
tasks.register('buildChanganReleaseAar') {
    dependsOn cleanOutputsAar
    dependsOn 'assembleChanganRelease'
    finalizedBy copyAarToTestDir

    group = 'AVL SDK'
    description = '构建长安版本 Release AAR'

    doLast {
        println """
                    ===================================
                    长安版本 Release AAR 构建完成！
                    输出目录: ${project.buildDir}/outputs/aar
                    复制目录: ${project.projectDir}/../tests
                    ===================================
                """
    }
}

// 构建全量版本 AAR
tasks.register('buildMainReleaseAar') {
    dependsOn cleanOutputsAar
    dependsOn 'assembleMainRelease'
    finalizedBy copyAarToTestDir

    group = 'AVL SDK'
    description = '构建全量版本 Release AAR'
    doLast {
        println """
                    ===================================
                    全量版本 Release AAR 构建完成！
                    输出目录: ${project.buildDir}/outputs/aar
                    复制目录: ${project.projectDir}/../tests
                    ===================================
                """
    }
}

// 清理 outputs/aar 目录的任务
task cleanOutputsAar(type: Delete) {
    delete "${project.buildDir}/outputs/aar"
}


fataar {
    transitive = true
}

dependencies {
    implementation "androidx.core:core-ktx:1.1.0"
    implementation "androidx.appcompat:appcompat:1.4.2"
    implementation "com.google.android.material:material:1.6.0"
    testImplementation "junit:junit:4.13.2"
    androidTestImplementation "androidx.test.ext:junit:1.2.1"
    androidTestImplementation "androidx.test.espresso:espresso-core:3.6.1"

    def isAarBuildTask = gradle.startParameter.taskNames.any { taskName ->
        taskName.contains('buildGreatwallReleaseAar') ||
        taskName.contains('buildChanganReleaseAar') ||
        taskName.contains('buildMainReleaseAar')
    }
    println("当前的任务是否是打包任务：" + isAarBuildTask)

    if (isAarBuildTask) {
        embed "com.google.code.gson:gson:2.9.0"
        embed 'org.apache.commons:commons-compress:1.21'
        embed project(path: ':avl_pc', configuration: 'default')
        embed project(path: ':avl_monitor', configuration: 'default')
        embed "io.reactivex.rxjava3:rxjava:3.0.13"
        embed 'io.reactivex.rxjava3:rxandroid:3.0.0'
        embed 'com.jaredrummler:apk-parser:1.0.2'
    } else {
        implementation "com.google.code.gson:gson:2.9.0"
        implementation 'org.apache.commons:commons-compress:1.21'
        implementation "io.reactivex.rxjava3:rxjava:3.0.13"
        implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
        implementation 'com.jaredrummler:apk-parser:1.0.2'
        api project(path: ':avl_pc')
        api project(path: ':avl_monitor')
    }
}

// Dokka文档配置
tasks.named("dokkaHtml") {
    outputDirectory.set(file("$buildDir/docs/dokka"))
    moduleName.set("AVL SDK")

    dokkaSourceSets {
        named("main") {
            sourceRoots.from(file("src/main/java"))
            perPackage {
                matchingRegex.set(".*\\.internal.*")
                suppress.set(true)
            }
            androidSdkPackage.set("android")
        }
    }
}