package com.antiy.demo.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.ArrayAdapter
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import com.antiy.avlsdk.AVLEngine
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityAntivirusSettingsBinding
import com.antiy.demo.databinding.DialogCacheSizeSettingBinding
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class AntivirusSettingsActivity : BaseActivity<ActivityAntivirusSettingsBinding>() {
    
    companion object {
        private const val PICK_FOLDER_REQUEST = 1
        private const val PREFS_NAME = "antivirus_settings"
        private const val KEY_CUSTOM_SCAN_PATH = "custom_scan_path"
        private const val KEY_CACHE_COUNT_LIMIT = "cache_history_count_limit"
        private const val KEY_CACHE_VALIDITY = "cache_history_timeout"
        private const val DEFAULT_CACHE_COUNT = 10000 // Default cache count
        private const val MIN_CACHE_COUNT = 100 // 最小缓存条目数限制
        private const val MAX_CACHE_COUNT = 100000 // 最大缓存条目数限制
    }
    
    private var cacheCountLimit = DEFAULT_CACHE_COUNT // 当前设置的缓存条目数限制

    override fun getViewBinding(inflater: LayoutInflater): ActivityAntivirusSettingsBinding {
        return ActivityAntivirusSettingsBinding.inflate(inflater)
    }

    override fun initView() {
        super.initView()
        setupStatusBar()
        setupToolbar()
        setupCustomPath()
//        setupSpinner()
        setupSwitches()
        setupButtons()
        setupCacheSettings()
    }

    private fun setupStatusBar() {
        // 确保状态栏正确显示
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 设置状态栏为亮色模式，图标为深色
            WindowCompat.getInsetsController(window, window.decorView)?.apply {
                isAppearanceLightStatusBars = true
            }
            
            // 设置状态栏为透明
            window.statusBarColor = ContextCompat.getColor(this, android.R.color.transparent)
            
            // 设置内容延伸到状态栏下方，但状态栏仍然可见
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            
            // 使用CoordinatorLayout时，设置为false让内容延伸到系统栏下方
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }

    private fun setupToolbar() {
        binding.btnBack.setOnClickListener {
            finish()
        }
    }

    private fun setupCustomPath() {
        // 从SharedPreferences加载保存的路径
        val sharedPrefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val savedPath = sharedPrefs.getString(KEY_CUSTOM_SCAN_PATH, null)
        savedPath?.let {
            binding.tvSelectedPath.text = it
        }

        binding.layoutCustomPath.setOnClickListener {
            // 启动文件选择器
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE)
            startActivityForResult(intent, PICK_FOLDER_REQUEST)
        }
    }

    private fun setupSpinner() {
        val validityOptions = arrayOf("7天", "15天", "30天", "60天")
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, validityOptions)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerCacheValidity.adapter = adapter
        binding.spinnerCacheValidity.setOnItemClickListener { parent, view, position, id ->
            // 获取当前选中的有效期
            val selectedValidity = validityOptions[position]
            // 保存选中的有效期
            getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .edit()
                .putString(KEY_CACHE_VALIDITY, selectedValidity)
                .apply()
            when(position){
                0 -> {
                    // 7天
                    AVLEngine.getInstance().updateConfig { config ->
                        config?.updateHistoryTimeout(7 * 24 * 60 * 60 * 1000)
                    }
                }
                1 -> {
                    // 15天
                    AVLEngine.getInstance().updateConfig { config ->
                        config?.updateHistoryTimeout(15 * 24 * 60 * 60 * 1000)
                    }
                }
                2 -> {
                    // 30天
                    AVLEngine.getInstance().updateConfig { config ->
                        config?.updateHistoryTimeout(30 * 24 * 60 * 60 * 1000L)
                    }
                }
                3 -> {
                    // 60天
                    AVLEngine.getInstance().updateConfig { config ->
                        config?.updateHistoryTimeout(60 * 24 * 60 * 60 * 1000L)
                    }
                }
            }
        }
    }

    private fun setupSwitches() {
        // 从SharedPreferences加载设置
        binding.apply {
            switchWhitelist.isChecked = true  // 示例默认值
            switchBlacklist.isChecked = true
            // 添加监听器保存设置
            switchWhitelist.setOnCheckedChangeListener { _, isChecked ->
                // 保存白名单设置
            }
        }
    }

    private fun setupButtons() {
        binding.apply {
            btnManageWhitelist.setOnClickListener {
                // 跳转到白名单管理页面
            }
            
            btnManageBlacklist.setOnClickListener {
                // 跳转到黑名单管理页面
            }
        }
    }
    
    private fun setupCacheSettings() {
        // 加载保存的缓存条目数限制
        val sharedPrefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        cacheCountLimit = sharedPrefs.getInt(KEY_CACHE_COUNT_LIMIT, DEFAULT_CACHE_COUNT)


        // 更新显示
        updateCacheCountDisplay()

        // 设置按钮点击事件
        binding.btnSetCache.setOnClickListener {
            showCacheCountSettingDialog()
        }

        binding.btnClearCache.setOnClickListener {
            // 清除缓存 (在实际应用中，这应该实际清除缓存)
            updateCacheCountDisplay()
        }

        // 整个区域点击事件
        binding.layoutCacheSize.setOnClickListener {
            showCacheCountSettingDialog()
        }
    }
    
    private fun updateCacheCountDisplay() {
        val displayText = "最大缓存条数:${cacheCountLimit} 条"
        AVLEngine.Logger.info("更新缓存显示: $displayText")

        // 确保在主线程中更新UI
        runOnUiThread {
            binding.tvCacheSize.text = displayText
        }
    }
    
    private fun showCacheCountSettingDialog() {
        val dialogBinding = DialogCacheSizeSettingBinding.inflate(layoutInflater)

        // 设置初始值
        dialogBinding.editCacheCount.setText(cacheCountLimit.toString())

        // 创建对话框
        val dialog = MaterialAlertDialogBuilder(this)
            .setView(dialogBinding.root)
            .create()

        // 设置保存按钮监听器
        dialogBinding.btnSave.setOnClickListener {
            val inputText = dialogBinding.editCacheCount.text.toString().trim()

            if (inputText.isEmpty()) {
                android.widget.Toast.makeText(this, "请输入缓存条目数", android.widget.Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            try {
                val inputCount = inputText.toInt()

                if (inputCount < MIN_CACHE_COUNT || inputCount > MAX_CACHE_COUNT) {
                    android.widget.Toast.makeText(
                        this,
                        "缓存条目数必须在 $MIN_CACHE_COUNT 到 $MAX_CACHE_COUNT 之间",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }

                // 保存新的缓存条目数限制
                AVLEngine.Logger.info("保存新的缓存条目数限制: $inputCount")
                cacheCountLimit = inputCount

                // 保存到 SharedPreferences
                getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                    .edit()
                    .putInt(KEY_CACHE_COUNT_LIMIT, cacheCountLimit)
                    .apply()

                // 更新显示
                AVLEngine.Logger.info("调用 updateCacheCountDisplay()")
                updateCacheCountDisplay()

                android.widget.Toast.makeText(this, "缓存条目数设置已保存", android.widget.Toast.LENGTH_SHORT).show()
                dialog.dismiss()


            } catch (e: NumberFormatException) {
                android.widget.Toast.makeText(this, "请输入有效的数字", android.widget.Toast.LENGTH_SHORT).show()
            }
        }

        // 设置取消按钮监听器
        dialogBinding.btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        // 显示对话框
        dialog.show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PICK_FOLDER_REQUEST && resultCode == Activity.RESULT_OK) {
            data?.data?.let { uri ->
                // 获取选择的目录路径
                val path = getPathFromUri(uri)
                AVLEngine.Logger.info("选择的目录路径: $path")
                path?.let {
                    // 更新UI显示
                    binding.tvSelectedPath.text = it
                    // 保存路径
                    getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                        .edit()
                        .putString(KEY_CUSTOM_SCAN_PATH, it)
                        .apply()
                }
            }
        }
    }

    private fun getPathFromUri(uri: Uri): String? {
        val path = uri.path ?: return null
        AVLEngine.Logger.info("getPathFromUri:  $path")
        // 从路径中提取实际的目录名
        val actualPath = when {
            // /tree/primary:samples  这种路径表示是手机的目录
            path.contains("primary:") -> {
                // 提取 primary: 后面的实际路径部分
                val segments = path.split("primary:")
                if (segments.size > 1) {
                    // 移除可能存在的前导斜杠
                    segments[1].trimStart('/')
                } else {
                    return null
                }
            }
            else -> {
                //  /tree/E62D-C937:sample20250605 这种路径表示是U盘的路径
                // 取E62D-C937:sample20250605字符串
                val actualPath = path.substringAfterLast("/").replace(":", "/")
                return "/mnt/media_rw/$actualPath"
            }
        }
        
        // 构建最终路径
        return "/storage/emulated/0/$actualPath"
    }
}